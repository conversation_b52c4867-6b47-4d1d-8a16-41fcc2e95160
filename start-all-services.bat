@echo off
echo 🚀 啟動 AI 客服系統所有服務

REM 檢查 .env 文件
if not exist ".env" (
    echo ⚠️  .env 文件不存在，從 .env.example 複製...
    copy .env.example .env
    echo 📝 請編輯 .env 文件設置您的 API 密鑰
    pause
)

echo 🔧 啟動後端服務器...
start "Backend Server" cmd /k "cd backend && go run main.go"

echo ⏳ 等待後端啟動...
timeout /t 5 /nobreak >nul

echo 🔧 啟動 MCP 服務器...
start "MCP Server" cmd /k "cd backend\mcp_server && go run main.go"

echo ⏳ 等待 MCP 服務器啟動...
timeout /t 3 /nobreak >nul

echo 🔧 啟動前端服務器...
start "Frontend Server" cmd /k "cd frontend && npm run dev"

echo 🎉 所有服務已啟動！
echo.
echo 📱 前端: http://localhost:3000
echo 🔧 後端 API: http://localhost:8080
echo 🛠️  MCP 服務器: http://localhost:8081
echo.
echo 💡 要停止服務，請關閉相應的命令窗口
echo.
pause
