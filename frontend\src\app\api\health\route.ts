import { NextResponse } from 'next/server';

export async function GET() {
  const healthData = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    services: {
      frontend: 'healthy',
      backend: 'unknown',
      mcp_server: 'unknown',
    },
  };

  try {
    // Check backend health
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8080';
    const backendResponse = await fetch(`${backendUrl}/api/v1/health`, {
      method: 'GET',
      timeout: 5000,
    });
    
    healthData.services.backend = backendResponse.ok ? 'healthy' : 'unhealthy';
  } catch (error) {
    healthData.services.backend = 'unreachable';
  }

  try {
    // Check MCP server health
    const mcpResponse = await fetch('http://localhost:8081/health', {
      method: 'GET',
      timeout: 5000,
    });
    
    healthData.services.mcp_server = mcpResponse.ok ? 'healthy' : 'unhealthy';
  } catch (error) {
    healthData.services.mcp_server = 'unreachable';
  }

  // Determine overall status
  const allServicesHealthy = Object.values(healthData.services).every(
    status => status === 'healthy'
  );
  
  healthData.status = allServicesHealthy ? 'healthy' : 'degraded';

  return NextResponse.json(healthData, {
    status: allServicesHealthy ? 200 : 503,
  });
}
