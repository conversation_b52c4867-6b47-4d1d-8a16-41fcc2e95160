package tools

import (
	"context"
	"fmt"

	"ai-customer-service/backend/mcp_server/internal/models"
)

type Tool interface {
	GetSchema() models.ToolSchema
	Execute(ctx context.Context, args map[string]interface{}) (string, error)
}

type ToolManager struct {
	tools map[string]Tool
}

func NewToolManager() *ToolManager {
	tm := &ToolManager{
		tools: make(map[string]Tool),
	}

	// Register tools
	tm.RegisterTool(&CurrentTimeTool{})
	tm.RegisterTool(&WebSearchTool{})

	return tm
}

func (tm *ToolManager) RegisterTool(tool Tool) {
	schema := tool.GetSchema()
	tm.tools[schema.Name] = tool
}

func (tm *ToolManager) GetAllTools() []models.ToolSchema {
	var schemas []models.ToolSchema
	for _, tool := range tm.tools {
		schemas = append(schemas, tool.GetSchema())
	}
	return schemas
}

func (tm *ToolManager) ExecuteTool(ctx context.Context, name string, args map[string]interface{}) (string, error) {
	tool, exists := tm.tools[name]
	if !exists {
		return "", fmt.Errorf("tool '%s' not found", name)
	}

	return tool.Execute(ctx, args)
}
