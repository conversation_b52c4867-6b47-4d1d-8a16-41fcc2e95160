# MCP Server

Model Context Protocol (MCP) server providing external tools for the AI system.

## Features

- Independent Go microservice
- RESTful API for tool discovery and execution
- Built-in tools: getCurrentTime, webSearch
- Extensible architecture for adding new tools

## Prerequisites

- Go 1.21 or later

## Installation

1. Install dependencies:
```bash
go mod tidy
```

2. Run the server:
```bash
go run main.go
```

The server will start on port 8081 by default.

## API Endpoints

### Tool Management
- `GET /tools` - Get available tools and their schemas
- `POST /execute` - Execute a tool with arguments

### Health Check
- `GET /health` - Health check endpoint

## Available Tools

### getCurrentTime
Get the current date and time.

**Parameters:**
- `timezone` (optional) - Timezone name (defaults to local)

**Example:**
```json
{
  "name": "getCurrentTime",
  "arguments": {
    "timezone": "UTC"
  }
}
```

### webSearch
Perform a simple web search using DuckDuckGo.

**Parameters:**
- `query` (required) - Search query string

**Example:**
```json
{
  "name": "webSearch",
  "arguments": {
    "query": "weather today"
  }
}
```

## Adding New Tools

1. Create a new tool file in `internal/tools/`
2. Implement the `Tool` interface:
   - `GetSchema()` - Return tool schema
   - `Execute()` - Execute the tool logic
3. Register the tool in `tool_manager.go`

## Integration

The main backend server can call MCP tools by:
1. Fetching available tools from `GET /tools`
2. Including tool schemas in AI prompts
3. Executing tools via `POST /execute` when the AI decides to use them
