'use client';

import { useEffect, useRef } from 'react';

// 定義對話訊息的結構
interface Message {
  role: 'user' | 'model';
  content: string;
}

interface ConversationViewProps {
  conversation: Message[];
  isThinking: boolean;
  transcription: string;
}

// ConversationView 元件用於顯示對話歷史、即時轉錄和思考狀態。
export default function ConversationView({ conversation, isThinking, transcription }: ConversationViewProps) {
  const endOfMessagesRef = useRef<HTMLDivElement | null>(null);

  // scrollToBottom 函式將檢視畫面滾動到最新的訊息。
  const scrollToBottom = () => {
    endOfMessagesRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // useEffect 鉤子用於在對話或轉錄更新時自動滾動。
  useEffect(() => {
    scrollToBottom();
  }, [conversation, transcription]);

  return (
    <div 
      className="w-full max-w-2xl h-64 p-4 border border-gray-600 rounded-lg mb-8 overflow-y-auto bg-gray-800"
      aria-live="polite"
      aria-atomic="false"
    >
      {conversation.map((msg, index) => (
        <div key={index} className={`mb-2 ${msg.role === 'user' ? 'text-right' : 'text-left'}`}>
          <span className={`inline-block p-2 rounded-lg ${msg.role === 'user' ? 'bg-blue-600' : 'bg-gray-700'}`}>
            {msg.content}
          </span>
        </div>
      ))}
      {transcription && <p className="text-gray-400">{transcription}</p>}
      {isThinking && <p className="text-gray-400 animate-pulse">思考中...</p>}
      <div ref={endOfMessagesRef} />
    </div>
  );
}
