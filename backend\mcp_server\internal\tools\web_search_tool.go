package tools

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"ai-customer-service/backend/mcp_server/internal/models"
)

type WebSearchTool struct{}

func (t *WebSearchTool) GetSchema() models.ToolSchema {
	return models.ToolSchema{
		Name:        "webSearch",
		Description: "Perform a simple web search",
		Parameters: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"query": map[string]interface{}{
					"type":        "string",
					"description": "Search query",
				},
			},
			"required": []string{"query"},
		},
	}
}

func (t *WebSearchTool) Execute(ctx context.Context, args map[string]interface{}) (string, error) {
	query, ok := args["query"].(string)
	if !ok || query == "" {
		return "", fmt.Errorf("query parameter is required")
	}

	// Simple web search using DuckDuckGo Instant Answer API
	searchURL := fmt.Sprintf("https://api.duckduckgo.com/?q=%s&format=json&no_html=1&skip_disambig=1", 
		url.QueryEscape(query))

	req, err := http.NewRequestWithContext(ctx, "GET", searchURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create search request: %w", err)
	}

	req.Header.Set("User-Agent", "AI-Customer-Service/1.0")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to perform search: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("search API returned status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read search response: %w", err)
	}

	// For simplicity, return the raw JSON response
	// In a production system, you'd parse this and extract relevant information
	result := string(body)
	
	// If the result is too long, truncate it
	if len(result) > 1000 {
		result = result[:1000] + "..."
	}

	// If no meaningful result, provide a fallback
	if strings.Contains(result, `"Abstract":""`) && strings.Contains(result, `"Answer":""`) {
		return fmt.Sprintf("搜尋查詢 '%s' 沒有找到直接答案，建議您查看相關網站獲取更多資訊。", query), nil
	}

	return result, nil
}
