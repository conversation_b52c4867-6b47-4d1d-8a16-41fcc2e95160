# RAG Document Indexing Tool

This CLI tool indexes documents for the RAG (Retrieval-Augmented Generation) system.

## Features

- Supports multiple document formats: TXT, PDF, DOCX, XLSX
- Chunks documents for optimal embedding
- Generates embeddings using Ollama
- Stores in PostgreSQL with pgvector

## Prerequisites

- Go 1.21 or later
- PostgreSQL with pgvector extension
- Ollama server running with embedding model

## Installation

1. Install dependencies:
```bash
go mod tidy
```

2. Ensure your .env file is configured with:
   - `DATABASE_URL`
   - `OLLAMA_API_URL`
   - `OLLAMA_EMBEDDING_MODEL_NAME`

## Usage

Index all documents in the RAGData directory:
```bash
go run main.go
```

Specify custom data directory:
```bash
go run main.go --data /path/to/documents
```

Specify custom config file:
```bash
go run main.go --config /path/to/.env
```

## Supported File Types

- **TXT** - Plain text files
- **PDF** - Portable Document Format
- **DOCX** - Microsoft Word documents
- **XLSX** - Microsoft Excel spreadsheets

## Configuration

The tool uses the following environment variables:

- `DATABASE_URL` - PostgreSQL connection string
- `OLLAMA_API_URL` - Ollama server URL
- `OLLAMA_EMBEDDING_MODEL_NAME` - Embedding model name
- `CHUNK_SIZE` - Text chunk size (default: 1000)
- `CHUNK_OVERLAP` - Chunk overlap size (default: 200)

## How It Works

1. Scans the specified directory for supported files
2. Parses each document to extract text content
3. Splits text into overlapping chunks
4. Generates embeddings for each chunk using Ollama
5. Stores chunks and embeddings in PostgreSQL

## Database Schema

The tool populates the `rag_documents` table with:
- `content` - Text chunk
- `embedding` - Vector embedding
- `source_file` - Original file path
- `chunk_index` - Chunk number within the document
