package parsers

import (
	"fmt"
	"strings"

	"github.com/unidoc/unipdf/v3/extractor"
	"github.com/unidoc/unipdf/v3/model"
)

type PdfParser struct{}

func (p *PdfParser) Parse(filePath string) (string, error) {
	// Open the PDF file
	f, err := model.NewPdfReader(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open PDF: %w", err)
	}

	var textContent strings.Builder

	// Extract text from each page
	numPages, err := f.GetNumPages()
	if err != nil {
		return "", fmt.Errorf("failed to get number of pages: %w", err)
	}

	for i := 1; i <= numPages; i++ {
		page, err := f.GetPage(i)
		if err != nil {
			continue // Skip problematic pages
		}

		ex, err := extractor.New(page)
		if err != nil {
			continue // Skip problematic pages
		}

		text, err := ex.ExtractText()
		if err != nil {
			continue // Skip problematic pages
		}

		textContent.WriteString(text)
		textContent.WriteString("\n")
	}

	return textContent.String(), nil
}
