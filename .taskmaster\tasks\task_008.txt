# Task ID: 8
# Title: Develop RAG Offline Indexing CLI Tool
# Status: pending
# Dependencies: 2
# Priority: medium
# Description: Develop a Go-based CLI tool for offline document indexing for the RAG system. This tool will parse documents, generate embeddings, and store them in the PostgreSQL vector database.
# Details:
In the `tools/indexing` directory, create a new Go CLI application using a library like `cobra`. The tool should accept a path to the `RAGData` directory. Implement parsers for PDF (`unidoc`), TXT (`os.ReadFile`), DOCX (`nguyenthenguyen/docx`), and XLSX (`xuri/excelize`). Use a recursive character splitter to chunk text. For each chunk, call the specified embedding model API (Ollama or Gemini) to get a vector. Connect to the PostgreSQL database and insert the content chunk and its vector into the `rag_documents` table.

# Test Strategy:
Place sample documents (PDF, TXT, DOCX, XLSX) in the `RAGData` folder. Run the CLI tool. Verify the tool runs without errors. Inspect the `rag_documents` table in PostgreSQL to confirm that document chunks and their corresponding vectors have been successfully stored.
