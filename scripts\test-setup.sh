#!/bin/bash

# AI Customer Service Setup Test Script

echo "🧪 Testing AI Customer Service Setup"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Test 1: Check if required commands exist
echo "📋 Checking prerequisites..."

command -v go >/dev/null 2>&1
print_status $? "Go is installed"

command -v node >/dev/null 2>&1
print_status $? "Node.js is installed"

command -v npm >/dev/null 2>&1
print_status $? "npm is installed"

command -v psql >/dev/null 2>&1
if [ $? -eq 0 ]; then
    print_status 0 "PostgreSQL client is available"
else
    print_warning "PostgreSQL client not found (optional for development)"
fi

# Test 2: Check project structure
echo ""
echo "📁 Checking project structure..."

[ -d "frontend" ]
print_status $? "Frontend directory exists"

[ -d "backend" ]
print_status $? "Backend directory exists"

[ -d "backend/mcp_server" ]
print_status $? "MCP server directory exists"

[ -d "tools/indexing" ]
print_status $? "Indexing tool directory exists"

[ -d "RAGData" ]
print_status $? "RAGData directory exists"

[ -f ".env.example" ]
print_status $? ".env.example file exists"

# Test 3: Check Go modules
echo ""
echo "🔧 Checking Go modules..."

cd backend
go mod verify >/dev/null 2>&1
print_status $? "Backend Go modules are valid"
cd ..

cd backend/mcp_server
go mod verify >/dev/null 2>&1
print_status $? "MCP server Go modules are valid"
cd ../..

cd tools/indexing
go mod verify >/dev/null 2>&1
print_status $? "Indexing tool Go modules are valid"
cd ../..

# Test 4: Check frontend dependencies
echo ""
echo "📦 Checking frontend setup..."

cd frontend
if [ -f "package.json" ]; then
    print_status 0 "Frontend package.json exists"
    
    if [ -d "node_modules" ]; then
        print_status 0 "Frontend dependencies installed"
    else
        print_warning "Frontend dependencies not installed (run: npm install)"
    fi
else
    print_status 1 "Frontend package.json missing"
fi
cd ..

# Test 5: Check environment configuration
echo ""
echo "⚙️  Checking environment configuration..."

if [ -f ".env" ]; then
    print_status 0 ".env file exists"
    
    # Check for required variables
    if grep -q "DATABASE_URL" .env; then
        print_status 0 "DATABASE_URL configured"
    else
        print_warning "DATABASE_URL not configured"
    fi
    
    if grep -q "GOOGLE_API_KEY" .env; then
        print_status 0 "GOOGLE_API_KEY configured"
    else
        print_warning "GOOGLE_API_KEY not configured (optional)"
    fi
    
    if grep -q "OLLAMA_API_URL" .env; then
        print_status 0 "OLLAMA_API_URL configured"
    else
        print_warning "OLLAMA_API_URL not configured (optional)"
    fi
else
    print_warning ".env file not found (copy from .env.example)"
fi

# Test 6: Check scripts
echo ""
echo "📜 Checking scripts..."

[ -x "scripts/start-dev.sh" ]
print_status $? "start-dev.sh is executable"

[ -x "scripts/index-documents.sh" ]
print_status $? "index-documents.sh is executable"

# Summary
echo ""
echo "📊 Setup Test Summary"
echo "====================="
echo "✅ If all tests pass, you can run: ./scripts/start-dev.sh"
echo "⚠️  If there are warnings, check the configuration but the system should still work"
echo "❌ If there are errors, please fix them before proceeding"
echo ""
echo "📚 Next steps:"
echo "1. Configure .env file with your API keys and database URL"
echo "2. Install frontend dependencies: cd frontend && npm install"
echo "3. Set up PostgreSQL database with pgvector extension"
echo "4. Run: ./scripts/start-dev.sh"
echo ""
echo "🎉 Happy coding!"
