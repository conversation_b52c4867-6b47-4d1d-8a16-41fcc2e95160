package models

import "time"

// ChatRequest represents the incoming chat request
type ChatRequest struct {
	Message string `json:"message"`
	Model   string `json:"model,omitempty"`
}

// ChatResponse represents the outgoing chat response
type ChatResponse struct {
	Response string `json:"response"`
	Error    string `json:"error,omitempty"`
}

// ConversationHistory represents a conversation record in the database
type ConversationHistory struct {
	ID          int       `json:"id"`
	UserMessage string    `json:"user_message"`
	AIResponse  string    `json:"ai_response"`
	Timestamp   time.Time `json:"timestamp"`
	SessionID   string    `json:"session_id"`
}

// RAGDocument represents a document chunk in the RAG system
type RAGDocument struct {
	ID         int       `json:"id"`
	Content    string    `json:"content"`
	Embedding  []float32 `json:"embedding,omitempty"`
	SourceFile string    `json:"source_file"`
	ChunkIndex int       `json:"chunk_index"`
	CreatedAt  time.Time `json:"created_at"`
}

// HealthResponse represents the health check response
type HealthResponse struct {
	Status    string `json:"status"`
	Timestamp string `json:"timestamp"`
	Version   string `json:"version"`
}
