package database

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5/pgxpool"
)

type DB struct {
	Pool *pgxpool.Pool
}

func Initialize(databaseURL string) (*DB, error) {
	// Create connection pool
	pool, err := pgxpool.New(context.Background(), databaseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	// Test connection
	if err := pool.Ping(context.Background()); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return &DB{Pool: pool}, nil
}

func (db *DB) Close() {
	if db.Pool != nil {
		db.Pool.Close()
	}
}

func (db *DB) InsertDocument(ctx context.Context, content string, embedding []float32, sourceFile string, chunkIndex int) error {
	query := `
		INSERT INTO rag_documents (content, embedding, source_file, chunk_index) 
		VALUES ($1, $2, $3, $4)
	`

	_, err := db.Pool.Exec(ctx, query, content, embedding, sourceFile, chunkIndex)
	return err
}

func (db *DB) ClearDocumentsBySource(ctx context.Context, sourceFile string) error {
	query := `DELETE FROM rag_documents WHERE source_file = $1`
	_, err := db.Pool.Exec(ctx, query, sourceFile)
	return err
}
