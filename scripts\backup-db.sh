#!/bin/bash

# Database Backup Script for AI Customer Service System

echo "💾 Starting database backup..."

# Configuration
BACKUP_DIR="./backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="ai_customer_service_backup_${TIMESTAMP}.sql"

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Check if running in Docker or local
if docker-compose ps postgres | grep -q "Up"; then
    echo "🐳 Detected Docker environment, using docker-compose..."
    
    # Backup using Docker
    docker-compose exec -T postgres pg_dump -U cicuser -d taskmaster_db > "$BACKUP_DIR/$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        echo "✅ Database backup completed successfully!"
        echo "📁 Backup saved to: $BACKUP_DIR/$BACKUP_FILE"
        
        # Compress the backup
        gzip "$BACKUP_DIR/$BACKUP_FILE"
        echo "🗜️  Backup compressed to: $BACKUP_DIR/$BACKUP_FILE.gz"
        
        # Show backup size
        BACKUP_SIZE=$(du -h "$BACKUP_DIR/$BACKUP_FILE.gz" | cut -f1)
        echo "📊 Backup size: $BACKUP_SIZE"
        
    else
        echo "❌ Database backup failed!"
        exit 1
    fi
    
elif command -v pg_dump &> /dev/null; then
    echo "🔧 Using local PostgreSQL client..."
    
    # Load environment variables
    if [ -f ".env" ]; then
        export $(grep -v '^#' .env | xargs)
    fi
    
    # Extract database connection info from DATABASE_URL
    if [ -n "$DATABASE_URL" ]; then
        pg_dump "$DATABASE_URL" > "$BACKUP_DIR/$BACKUP_FILE"
    else
        echo "❌ DATABASE_URL not found in .env file"
        exit 1
    fi
    
    if [ $? -eq 0 ]; then
        echo "✅ Database backup completed successfully!"
        echo "📁 Backup saved to: $BACKUP_DIR/$BACKUP_FILE"
        
        # Compress the backup
        gzip "$BACKUP_DIR/$BACKUP_FILE"
        echo "🗜️  Backup compressed to: $BACKUP_DIR/$BACKUP_FILE.gz"
        
        # Show backup size
        BACKUP_SIZE=$(du -h "$BACKUP_DIR/$BACKUP_FILE.gz" | cut -f1)
        echo "📊 Backup size: $BACKUP_SIZE"
        
    else
        echo "❌ Database backup failed!"
        exit 1
    fi
    
else
    echo "❌ Neither Docker nor local PostgreSQL client found!"
    echo "Please install PostgreSQL client or use Docker environment."
    exit 1
fi

# Clean up old backups (keep last 7 days)
echo "🧹 Cleaning up old backups..."
find "$BACKUP_DIR" -name "ai_customer_service_backup_*.sql.gz" -mtime +7 -delete
echo "✅ Old backups cleaned up"

# List recent backups
echo ""
echo "📋 Recent backups:"
ls -lah "$BACKUP_DIR"/ai_customer_service_backup_*.sql.gz | tail -5

echo ""
echo "💡 To restore a backup:"
echo "   Docker: gunzip -c backup_file.sql.gz | docker-compose exec -T postgres psql -U cicuser -d taskmaster_db"
echo "   Local:  gunzip -c backup_file.sql.gz | psql \$DATABASE_URL"
