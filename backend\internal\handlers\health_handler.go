package handlers

import (
	"encoding/json"
	"net/http"
	"time"

	"ai-customer-service/backend/internal/models"
)

func HandleHealth(w http.ResponseWriter, r *http.Request) {
	response := models.HealthResponse{
		Status:    "healthy",
		Timestamp: time.Now().Format(time.RFC3339),
		Version:   "1.0.0",
	}

	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}
