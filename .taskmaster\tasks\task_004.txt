# Task ID: 4
# Title: Implement Real-Time Speech-to-Text (STT)
# Status: pending
# Dependencies: 3
# Priority: high
# Description: Implement real-time voice capture and transcription using the Web Speech API. This includes handling microphone permissions and displaying the transcribed text on the UI.
# Details:
In the frontend, use the `SpeechRecognition` interface from the Web Speech API. On component mount or app start, request microphone permission. Implement the 'Start/Stop Voice' button logic to start and stop the recognition service. Set `continuous` to true for always-on input. As results are received, update the application state with the transcribed text and display it in the conversation area. Provide visual feedback (e.g., a pulsing icon) when the microphone is active.

# Test Strategy:
Unit test the state changes for starting/stopping listening. Manually test in Chrome and Firefox. Verify the microphone permission prompt appears correctly. Speak into the microphone and confirm that the transcribed text appears in real-time on the screen. Test the 'Stop Voice' functionality.
