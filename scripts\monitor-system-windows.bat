@echo off
echo 📊 AI Customer Service System Monitor (Windows)
echo ====================================

REM Function to check service health
echo 🔗 Service Health Checks:

REM Check Frontend
curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Frontend is healthy
) else (
    echo ❌ Frontend is not responding
)

REM Check Backend API
curl -s http://localhost:8080/api/v1/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Backend API is healthy
) else (
    echo ❌ Backend API is not responding
)

REM Check MCP Server
curl -s http://localhost:8081/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ MCP Server is healthy
) else (
    echo ❌ MCP Server is not responding
)

echo.
echo 🗄️  Database Status:
psql "postgresql://cicuser:pi@localhost:5432/taskmaster_db?sslmode=disable" -c "SELECT 1;" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ PostgreSQL is ready
    
    REM Get database stats
    for /f "tokens=*" %%i in ('psql "postgresql://cicuser:pi@localhost:5432/taskmaster_db?sslmode=disable" -t -c "SELECT count(*) FROM conversation_history;"') do set CONV_COUNT=%%i
    for /f "tokens=*" %%i in ('psql "postgresql://cicuser:pi@localhost:5432/taskmaster_db?sslmode=disable" -t -c "SELECT count(*) FROM rag_documents;"') do set RAG_COUNT=%%i
    
    echo   💬 Conversation records: %CONV_COUNT%
    echo   📚 RAG documents: %RAG_COUNT%
) else (
    echo ❌ PostgreSQL connection failed
)

echo.
echo 🧪 API Tests:

REM Test chat endpoint
curl -s -X POST http://localhost:8080/api/v1/chat -H "Content-Type: application/json" -d "{\"message\":\"健康檢查\",\"model\":\"Gemini\"}" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Chat API is working
) else (
    echo ❌ Chat API test failed
)

REM Test MCP tools endpoint
curl -s http://localhost:8081/tools >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ MCP Tools API is working
) else (
    echo ❌ MCP Tools API test failed
)

echo.
echo 📋 Summary:
echo ===========
echo 🌐 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:8080
echo 🛠️  MCP Server: http://localhost:8081
echo 🗄️  Database: localhost:5432
echo.
echo 🔄 Monitor refreshed at: %date% %time%
echo.
pause
