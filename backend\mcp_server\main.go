package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"ai-customer-service/backend/mcp_server/internal/handlers"
	"ai-customer-service/backend/mcp_server/internal/tools"

	"github.com/gorilla/mux"
	"github.com/rs/cors"
)

func main() {
	// Initialize tool manager
	toolManager := tools.NewToolManager()

	// Initialize handlers
	toolHandler := handlers.NewToolHandler(toolManager)

	// Setup routes
	router := mux.NewRouter()
	
	// MCP endpoints
	router.HandleFunc("/tools", toolHandler.HandleGetTools).Methods("GET", "OPTIONS")
	router.HandleFunc("/execute", toolHandler.HandleExecuteTool).Methods("POST", "OPTIONS")
	router.HandleFunc("/health", handlers.HandleHealth).Methods("GET")

	// Setup CORS
	c := cors.New(cors.Options{
		AllowedOrigins:   []string{"*"},
		AllowedMethods:   []string{"GET", "POST", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
	})

	handler := c.Handler(router)

	// Create server
	server := &http.Server{
		Addr:         ":8081",
		Handler:      handler,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Start server in a goroutine
	go func() {
		log.Println("MCP Server starting on port 8081")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("MCP Server failed to start: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down MCP server...")

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("MCP Server forced to shutdown: %v", err)
	}

	log.Println("MCP Server exited")
}
