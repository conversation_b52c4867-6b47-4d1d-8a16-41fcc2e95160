#!/bin/bash

# Comprehensive Test Suite for AI Customer Service System

echo "🧪 Running AI Customer Service Test Suite"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to run a test
run_test() {
    local test_name=$1
    local test_command=$2
    local expected_result=${3:-0}
    
    echo -e "${BLUE}🔍 Testing: $test_name${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if eval "$test_command" > /dev/null 2>&1; then
        local result=$?
        if [ $result -eq $expected_result ]; then
            echo -e "${GREEN}✅ PASS: $test_name${NC}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}❌ FAIL: $test_name (exit code: $result)${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        echo -e "${RED}❌ FAIL: $test_name${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# Function to test HTTP endpoint
test_http_endpoint() {
    local endpoint_name=$1
    local url=$2
    local expected_status=${3:-200}
    local method=${4:-GET}
    
    echo -e "${BLUE}🌐 Testing HTTP: $endpoint_name${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    local response_code
    if [ "$method" = "POST" ]; then
        response_code=$(curl -s -X POST "$url" \
            -H "Content-Type: application/json" \
            -d '{"message":"test","model":"Gemini"}' \
            -w "%{http_code}" -o /dev/null)
    else
        response_code=$(curl -s "$url" -w "%{http_code}" -o /dev/null)
    fi
    
    if [ "$response_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS: $endpoint_name (HTTP $response_code)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ FAIL: $endpoint_name (HTTP $response_code, expected $expected_status)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

echo ""
echo "📋 1. Environment Tests"
echo "======================="

# Test 1: Check required commands
run_test "Go installation" "command -v go"
run_test "Node.js installation" "command -v node"
run_test "npm installation" "command -v npm"

# Test 2: Check project structure
run_test "Frontend directory exists" "[ -d frontend ]"
run_test "Backend directory exists" "[ -d backend ]"
run_test "MCP server directory exists" "[ -d backend/mcp_server ]"
run_test "Indexing tool directory exists" "[ -d tools/indexing ]"
run_test "RAGData directory exists" "[ -d RAGData ]"

echo ""
echo "🔧 2. Build Tests"
echo "================"

# Test 3: Go module validation
echo -e "${BLUE}🔍 Testing Go modules...${NC}"
cd backend
run_test "Backend Go modules" "go mod verify"
cd ../backend/mcp_server
run_test "MCP server Go modules" "go mod verify"
cd ../../tools/indexing
run_test "Indexing tool Go modules" "go mod verify"
cd ../..

# Test 4: Frontend build test
echo -e "${BLUE}🔍 Testing frontend build...${NC}"
cd frontend
if [ -d "node_modules" ]; then
    run_test "Frontend TypeScript check" "npm run build --dry-run"
else
    echo -e "${YELLOW}⚠️  Skipping frontend build test (dependencies not installed)${NC}"
fi
cd ..

echo ""
echo "🌐 3. Service Health Tests"
echo "========================="

# Wait a moment for services to be ready
sleep 2

# Test 5: HTTP endpoints
test_http_endpoint "Frontend health" "http://localhost:3000/api/health"
test_http_endpoint "Backend health" "http://localhost:8080/api/v1/health"
test_http_endpoint "MCP server health" "http://localhost:8081/health"

# Test 6: API functionality
test_http_endpoint "Chat API" "http://localhost:8080/api/v1/chat" "200" "POST"
test_http_endpoint "History API" "http://localhost:8080/api/v1/history"
test_http_endpoint "MCP tools API" "http://localhost:8081/tools"

echo ""
echo "🗄️  4. Database Tests"
echo "==================="

# Test 7: Database connectivity
if command -v docker-compose &> /dev/null && docker-compose ps postgres | grep -q "Up"; then
    run_test "PostgreSQL connection" "docker-compose exec postgres pg_isready -U cicuser -d taskmaster_db"
    run_test "pgvector extension" "docker-compose exec -T postgres psql -U cicuser -d taskmaster_db -c 'SELECT * FROM pg_extension WHERE extname = \"vector\";'"
    run_test "Conversation history table" "docker-compose exec -T postgres psql -U cicuser -d taskmaster_db -c 'SELECT 1 FROM conversation_history LIMIT 1;'"
    run_test "RAG documents table" "docker-compose exec -T postgres psql -U cicuser -d taskmaster_db -c 'SELECT 1 FROM rag_documents LIMIT 1;'"
elif [ -f ".env" ] && command -v psql &> /dev/null; then
    export $(grep -v '^#' .env | xargs)
    if [ -n "$DATABASE_URL" ]; then
        run_test "PostgreSQL connection" "psql \"$DATABASE_URL\" -c 'SELECT 1;'"
        run_test "pgvector extension" "psql \"$DATABASE_URL\" -c 'SELECT * FROM pg_extension WHERE extname = \"vector\";'"
        run_test "Conversation history table" "psql \"$DATABASE_URL\" -c 'SELECT 1 FROM conversation_history LIMIT 1;'"
        run_test "RAG documents table" "psql \"$DATABASE_URL\" -c 'SELECT 1 FROM rag_documents LIMIT 1;'"
    else
        echo -e "${YELLOW}⚠️  DATABASE_URL not found, skipping database tests${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  No database connection available, skipping database tests${NC}"
fi

echo ""
echo "🤖 5. AI Integration Tests"
echo "=========================="

# Test 8: AI service configuration
if [ -f ".env" ]; then
    export $(grep -v '^#' .env | xargs)
    
    if [ -n "$GOOGLE_API_KEY" ]; then
        echo -e "${GREEN}✅ Google API key configured${NC}"
    else
        echo -e "${YELLOW}⚠️  Google API key not configured${NC}"
    fi
    
    if [ -n "$OLLAMA_API_URL" ]; then
        echo -e "${GREEN}✅ Ollama API URL configured${NC}"
        # Test Ollama connectivity
        if curl -s "$OLLAMA_API_URL/api/tags" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Ollama server is reachable${NC}"
        else
            echo -e "${YELLOW}⚠️  Ollama server is not reachable${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  Ollama API URL not configured${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  .env file not found, skipping AI configuration tests${NC}"
fi

echo ""
echo "📊 Test Results Summary"
echo "======================="
echo -e "Total Tests: ${BLUE}$TOTAL_TESTS${NC}"
echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 All tests passed!${NC}"
    echo "✅ System is ready for use"
    exit 0
else
    echo -e "${RED}❌ Some tests failed${NC}"
    echo "⚠️  Please check the failed tests and fix any issues"
    exit 1
fi
