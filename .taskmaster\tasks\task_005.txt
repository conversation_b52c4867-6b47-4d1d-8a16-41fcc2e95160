# Task ID: 5
# Title: Backend LLM Integration and Conversation Memory
# Status: pending
# Dependencies: 2
# Priority: high
# Description: Create a backend API endpoint to receive user text, communicate with the Google Gemini LLM, and return the generated response. Also, implement logic to save and retrieve conversation context from a `memory.txt` file.
# Details:
In the Go backend, create a RESTful endpoint, e.g., `POST /api/chat`. This endpoint will receive a JSON payload with the user's message. Read the existing context from `memory.txt`, prepend it to the user's message, and send the combined text to the Google Gemini API (`gemini-2.5-pro`) using the `GOOGLE_API_KEY`. After receiving the response, append the user message and AI response to `memory.txt`. Implement a `POST /api/chat/clear` endpoint to erase `memory.txt`.

# Test Strategy:
Use an API client like Postman or curl to send requests to the `/api/chat` endpoint. Verify that a valid response is received from the Gemini API. Check the `memory.txt` file to ensure the conversation history is being appended correctly. Test the `/api/chat/clear` endpoint.
