package parsers

import (
	"fmt"
	"strings"

	"github.com/nguyenthenguyen/docx"
)

type DocxParser struct{}

func (p *DocxParser) Parse(filePath string) (string, error) {
	// Read the docx file
	r, err := docx.ReadDocxFile(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to read DOCX file: %w", err)
	}
	defer r.Close()

	// Extract text content
	docx := r.Editable()
	content := docx.GetContent()

	// Clean up the content (remove excessive whitespace)
	lines := strings.Split(content, "\n")
	var cleanLines []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			cleanLines = append(cleanLines, line)
		}
	}

	return strings.Join(cleanLines, "\n"), nil
}
