# Task ID: 9
# Title: Integrate RAG into AI Response Generation
# Status: pending
# Dependencies: 5, 8
# Priority: medium
# Description: Integrate the RAG functionality into the backend chat endpoint to provide context-aware answers based on the indexed documents.
# Details:
Modify the `/api/chat` endpoint logic. When a user query is received, first generate an embedding for the query using the same embedding model as the indexing tool. Perform a similarity search (e.g., cosine distance) against the vectors in the `rag_documents` table using `pgvector`'s `<=>` operator. Retrieve the top 3-5 most relevant document chunks. Prepend this retrieved context to the prompt sent to the Gemini LLM, instructing it to use the context to answer the user's question.

# Test Strategy:
Index a document with specific, non-public information. Ask a question that can only be answered using the information from that document. Verify that the AI's response is accurate and based on the provided context. Check backend logs to ensure the similarity search is being performed.
