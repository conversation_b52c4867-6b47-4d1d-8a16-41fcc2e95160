# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencies
node_modules/
*/node_modules/

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work
vendor/

# Build outputs
/backend/server
/backend/mcp_server/mcp_server
/tools/indexing/indexer
dist/
build/

# Next.js
.next/
out/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
lerna-debug.log*

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Memory files
memory.txt

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Temporary folders
tmp/
temp/

# Local development
.local/