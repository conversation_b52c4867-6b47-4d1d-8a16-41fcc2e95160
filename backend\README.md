# AI Customer Service Backend

This is the Go backend server for the AI Customer Service system.

## Features

- RESTful API for chat interactions
- Integration with Google Gemini and Ollama AI models
- PostgreSQL database with pgvector for conversation history and RAG
- Conversation memory management
- RAG (Retrieval-Augmented Generation) support
- CORS enabled for frontend integration

## Prerequisites

- Go 1.21 or later
- PostgreSQL with pgvector extension
- Google Gemini API key (optional)
- Ollama server running (optional)

## Installation

1. Install dependencies:
```bash
go mod tidy
```

2. Set up environment variables (copy from .env.example):
```bash
cp ../.env.example ../.env
# Edit .env with your actual values
```

3. Ensure PostgreSQL is running with pgvector extension installed

4. Run the server:
```bash
go run main.go
```

The server will start on port 8080 by default.

## API Endpoints

### Chat API
- `POST /api/v1/chat` - Send a message to the AI
- `POST /api/v1/chat/clear` - Clear conversation memory
- `GET /api/v1/history` - Get conversation history

### Health Check
- `GET /api/v1/health` - Health check endpoint

## Configuration

The server uses environment variables for configuration:

- `DATABASE_URL` - PostgreSQL connection string
- `GOOGLE_API_KEY` - Google Gemini API key
- `OLLAMA_API_URL` - Ollama server URL
- `OLLAMA_MODEL` - Ollama model name
- `OLLAMA_EMBEDDING_MODEL_NAME` - Ollama embedding model
- `GEMINI_MODEL` - Gemini model name
- `SYSTEM_PROMPT` - AI system prompt
- `PORT` - Server port (default: 8080)

## Database Schema

The server automatically creates the following tables:

- `conversation_history` - Stores chat history
- `rag_documents` - Stores document chunks and embeddings for RAG

## RAG Integration

The server supports RAG functionality by:
1. Generating embeddings for user queries
2. Performing similarity search against stored document embeddings
3. Including relevant context in AI prompts

Use the indexing tool in `tools/indexing/` to populate the RAG database.
