# Voice-Enabled AI Chat Application - Product Requirements Document

## 1. Executive Summary

### 1.1 Project Overview
The Voice-Enabled AI Chat Application is a real-time conversational interface that allows users to interact with AI through voice commands and receive spoken responses. The application provides a seamless voice-to-voice experience with visual feedback and conversation history.

### 1.2 Objectives
- Create an intuitive voice-first AI interaction platform
- Provide real-time speech recognition and synthesis
- Deliver natural conversational AI experiences
- Maintain conversation context and history
- Ensure accessibility and ease of use

### 1.3 Success Metrics
- Voice recognition accuracy > 95%
- Response time < 2 seconds
- User engagement > 5 minutes per session
- Error rate < 5%

## 2. Product Features

### 2.1 Core Features

#### 2.1.1 Always-On Voice Input
- **Description**: Voice input is enabled by default, allowing for continuous conversation until manually stopped.
- **Behavior**: 
  - The microphone is active by default when the application starts.
  - A "Stop Voice" button is available to disable the microphone.
  - Visual feedback indicates when the microphone is active (e.g., a persistent icon).
  - Pressing "Stop Voice" deactivates the microphone and stops voice input.
  - A "Start Voice" button will be available to re-enable the microphone.

#### 2.1.2 Speech-to-Text Conversion
- **Description**: Real-time transcription of user voice input
- **Features**:
  - Live transcription display
  - Confidence scoring
  - Language detection
  - Noise cancellation support

#### 2.1.3 AI Response Generation
- **Description**: Integration with LLM for intelligent responses
- **Features**:
  - Context-aware responses
  - Conversation memory (save to memory.txt file and can be erased and initialized by turning off website or click the clean memory button)
  - Customizable AI personality
  - Response filtering and safety

#### 2.1.4 Text-to-Speech Playback
- **Description**: Convert AI responses to natural speech
- **Features**:
  - Multiple voice options
  - Adjustable speech rate
  - Volume control
  - Interrupt capability

#### 2.1.5 Conversation History
- **Description**: Visual display of conversation flow
- **Features**:
  - Scrollable chat interface
  - Timestamp display
  - Export conversation
  - Search functionality

#### 2.1.6 Knowledge Base Integration (RAG)
- **Description**: Enhance the AI's knowledge by allowing it to retrieve information from a local document repository before answering questions. The source documents for RAG are stored in the `RAGData` directory.
- **Features**:
  - Offline indexing pipeline to process documents (PDF, TXT, DOCX, XLSX) from the `RAGData` folder.
  - Document content is chunked, converted to vector embeddings, and stored in a specialized vector database (PostgreSQL with pgvector).
  - When a user asks a question, the system performs a similarity search on the vector database to find relevant context.
  - The retrieved context is used to augment the prompt sent to the LLM, enabling more accurate and context-specific answers.

#### 2.1.7 Extensible Tool Use (MCP)
- **Description**: Enable the AI to use external tools to perform actions and retrieve real-time information, enhancing its capabilities beyond its built-in knowledge.
- **Features**:
  - A dedicated MCP (Model Context Protocol) server that exposes a set of tools (e.g., get current time, web search).
  - The LLM can dynamically decide to call one or more tools based on the user's query.
  - The system executes the tool call, retrieves the result, and sends it back to the LLM to formulate a final, user-facing answer.
- **Demo Example**:
  - User says: "Open Google search and look up Taipei 101 information". The system detects this as a web search request. It invokes the WebSearch tool from the MCP server with the query "Taipei 101 information". The search results (e.g., summary or top links) are returned to the LLM. The LLM generates a spoken and visual response: "Here's what I found about Taipei 101...". Optionally, the system can also open a new browser tab with the Google search results for the user.

### 2.2 Secondary Features

#### 2.2.1 Settings & Preferences
- Voice selection
- Speech rate adjustment
- Language preferences
- Theme customization

#### 2.2.2 Accessibility Features
- Keyboard navigation
- Screen reader support
- High contrast mode
- Font size adjustment

## 3. User Stories

### 3.1 Primary User Stories
1. **As a user**, I want voice input to be on by default for a seamless conversation, with a clear button to toggle the microphone on and off for control.
2. **As a user**, I want to see my speech transcribed in real-time so that I can verify what the system understood
3. **As a user**, I want to receive AI responses as spoken audio so that I can continue the conversation naturally
4. **As a user**, I want to view my conversation history so that I can reference previous exchanges
5. **As a user**, I want to interrupt the AI's response so that I can ask follow-up questions immediately
6. **As a user**, I want the AI to answer questions based on my private documents so that I can get information specific to my needs.
7. **As a user**, I want to ask for real-time information, like "what time is it?", and get an accurate, up-to-date answer.

### 3.2 Secondary User Stories
1. **As a user**, I want to customize the AI's voice so that the experience feels more personal
2. **As a user**, I want to export my conversations so that I can save important information
3. **As a user**, I want the interface to work on mobile devices so that I can use it anywhere

## 4. Technical Requirements

### 4.1 Frontend Requirements
- **Framework**: React with Next.js
- **Speech Recognition**: Web Speech API (SpeechRecognition)
- **Speech Synthesis**: Web Speech API (SpeechSynthesis)
- **Audio Processing**: MediaRecorder API
- **Styling**: Tailwind CSS or styled-components
- **State Management**: React Context or Redux Toolkit

### 4.2 Backend Requirements
- **Runtime**: Golang
- **LLM Integration**: Google Gemini gemini-2.5-pro API
- **Database**: PostgreSQL with pgvector extension for conversation and vector storage
- **Authentication**: JWT-based (optional for MVP)
- **File Storage**: Local filesystem or cloud storage

### 4.3 API Requirements
- RESTful API design
- WebSocket support for real-time features
- Rate limiting and security measures
- Comprehensive error handling
- API documentation with OpenAPI/Swagger

### 4.4 RAG Pipeline Requirements
- **CLI Tool**: A dedicated Go-based CLI tool for offline indexing, located in the `tools/indexing` directory.
- **Document Parsers**: Support for PDF (`unidoc`), TXT (`os`), DOCX (`nguyenthenguyen/docx`), and XLSX (`xuri/excelize`).
- **Text Chunker**: Recursive character-based text splitter.
- **Embedding Model**: Integration with an embedding model endpoint (Ollama or Gemini) to generate vectors.
- **Vector Store**: PostgreSQL with `pgvector` extension.

### 4.5 MCP Server Requirements
- **Architecture**: Independent Go-based microservice.
- **Protocol**: Exposes `/tools` for schema definitions and `/execute` for tool execution.
- **Tooling**: Initial tools for getting current time and performing simple web searches.

### 4.6 Project Structure
- **Description**: The project will be organized into the following top-level directories to ensure a clear separation of concerns.
- **Directories**:
  - `frontend/`: Contains all the Next.js web interface code.
  - `backend/`: Contains the main Go backend service, including API handlers and business logic.
  - `tools/`: Contains supplementary command-line tools, such as the RAG indexing script.
  - `PRD/`: Contains all product-related documentation, including this document, mockups, and other specifications.

## 5. Non-Functional Requirements

### 5.1 Performance
- Initial page load: < 3 seconds
- Voice processing latency: < 500ms
- AI response time: < 2 seconds
- Concurrent users: 100+

### 5.2 Security
- Input validation and sanitization
- Rate limiting on API endpoints
- Secure API key management
- HTTPS enforcement
- Content filtering

### 5.3 Compatibility
- Modern browsers (Chrome 80+, Firefox 75+, Microsoft Edge)
- Mobile responsive design
- Progressive Web App capabilities
- Offline functionality (limited)

### 5.4 Scalability
- Horizontal scaling capability
- Database optimization
- CDN integration
- Caching strategies

## 6. User Interface Requirements

### 6.1 Layout Structure
- Header with app title and settings
- Main conversation area (scrollable)
- Start/Stop Voice button (prominent, center-bottom)
- Status indicators and controls

### 6.2 Visual Design
- **Reference**: The overall web UI design should follow the mockup provided in `UI.png`.
- Clean, minimalist interface
- High contrast for accessibility
- Responsive design for all screen sizes
- Smooth animations and transitions
- Loading states and feedback

### 6.3 Interaction Patterns
- Touch-friendly button sizes (44px minimum)
- Clear visual feedback for all actions
- Intuitive gesture support
- Keyboard shortcuts for power users

## 7. Data Requirements

### 7.1 Conversation Data
- User messages (text and audio metadata)
- AI responses (text and synthesis metadata)
- Timestamps and session information
- User preferences and settings

### 7.2 Audio Data
- Temporary audio file storage
- Audio format: WebM or MP3
- Maximum file size: 10MB per recording
- Automatic cleanup after processing

## 8. Integration Requirements

### 8.1 Third-Party Services
- **Google Gemini API**: For AI response generation
- **Web Speech API**: For browser-based speech processing
- **Optional**: Google Cloud Speech-to-Text for enhanced accuracy
- **Optional**: Azure Cognitive Services for additional voice options

### 8.2 Browser APIs
- MediaRecorder API for audio capture
- SpeechRecognition API for speech-to-text
- SpeechSynthesis API for text-to-speech
- Permissions API for microphone access

## 9. Error Handling & Edge Cases

### 9.1 Common Error Scenarios
- Microphone permission denied
- Network connectivity issues
- API rate limiting
- Unsupported browser features
- Audio processing failures

### 9.2 Error Recovery
- Graceful degradation to text-only mode
- Retry mechanisms for failed requests
- Clear error messages and guidance
- Fallback options for unsupported features

## 10. Testing Requirements

### 10.1 Unit Testing
- Component testing for React components
- API endpoint testing
- Speech processing function testing
- Error handling validation

### 10.2 Integration Testing
- End-to-end conversation flows
- Cross-browser compatibility
- Mobile device testing
- Performance testing under load

### 10.3 User Acceptance Testing
- Voice recognition accuracy testing
- User experience validation
- Accessibility compliance testing
- Real-world usage scenarios

## 11. Deployment & DevOps

### 11.1 Development Environment
- Local development with hot reload
- **Environment Variable Management**: Key configurations will be managed via a `.env` file in the project root to allow for flexibility and security. The following variables must be defined:
  - `DATABASE_URL`: The connection string for the PostgreSQL database (e.g., "postgresql://cicuser:pi@localhost:5432/taskmaster_db?sslmode=disable").
  - `GOOGLE_API_KEY`: The API key for Google Gemini services.
  - `OLLAMA_API_URL`: The base URL for the Ollama API service (e.g., "http://************:11434").
  - `OLLAMA_MODEL`: The name of the Ollama model for AI response generation.
  - `OLLAMA_EMBEDDING_MODEL_NAME`: The name of the embedding model used by the RAG pipeline.
  - `GEMINI_MODEL`: The name of the Gemini model for AI response generation.
  - `SYSTEM_PROMPT`: The default system prompt that defines the AI's personality and response format (e.g., "你是美而美早餐店的AI客服人員...").
- Database seeding and migrations
- API mocking for development

### 11.2 Production Deployment
- Containerized deployment (Docker)
- CI/CD pipeline setup
- Environment-specific configurations
- Monitoring and logging
- Backup and recovery procedures

## 12. Future Enhancements

### 12.1 Phase 2 Features
- Multi-language support
- Voice training and personalization
- Integration with external services
- Advanced conversation analytics
- Collaborative conversations

### 12.2 Advanced Features
- Emotion detection in voice
- Background noise filtering
- Voice biometric authentication
- AI personality customization
- Integration with smart home devices

## 13. Risks & Mitigation

### 13.1 Technical Risks
- **Browser compatibility**: Implement feature detection and fallbacks
- **API rate limits**: Implement caching and request optimization
- **Voice recognition accuracy**: Provide manual text input option
- **Latency issues**: Optimize API calls and implement local processing where possible

### 13.2 Business Risks
- **User adoption**: Focus on intuitive UX and clear value proposition
- **Privacy concerns**: Implement transparent data handling policies
- **Competition**: Differentiate through superior voice experience
- **Scalability costs**: Plan for efficient resource utilization

## 14. Success Criteria

### 14.1 MVP Success Criteria
- Functional voice-to-voice conversation
- < 2 second response times
- 95%+ voice recognition accuracy
- Mobile-responsive interface
- Basic conversation history

### 14.2 Long-term Success Criteria
- 1000+ active users
- 90%+ user satisfaction score
- < 1% error rate
- Multi-platform availability
- Advanced AI capabilities

### 15. Directory Structure Definition
To maintain a clean and organized project structure, the application is divided into clearly defined top-level directories, each with a specific purpose:
- frontend: Contains all frontend source code, including the React + Next.js application, UI components, styles (Tailwind CSS), and browser-based speech interaction logic.
- backend: Contains the main backend service written in Go, including API endpoints, business logic, RAG integration, vector database access, and MCP server components.
- RAGData: A folder where users can upload reference documents (PDF, TXT, DOCX, XLSX) used for building the RAG knowledge base. These files are indexed and stored as vector embeddings.
- PRD: Contains all product documentation, such as the product requirements document (PRD), design mockups, diagrams, and planning materials.
- tools: Includes utility CLI tools such as the offline RAG indexing script and other supporting utilities.


