package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"

	"ai-customer-service/tools/indexing/internal/config"
	"ai-customer-service/tools/indexing/internal/database"
	"ai-customer-service/tools/indexing/internal/indexer"
	"ai-customer-service/tools/indexing/internal/parsers"

	"github.com/joho/godotenv"
	"github.com/spf13/cobra"
)

var (
	dataDir    string
	configFile string
)

func main() {
	var rootCmd = &cobra.Command{
		Use:   "indexer",
		Short: "RAG Document Indexing Tool",
		Long:  "A CLI tool for indexing documents into the RAG vector database",
		Run:   runIndexer,
	}

	rootCmd.Flags().StringVarP(&dataDir, "data", "d", "../../RAGData", "Path to the data directory containing documents")
	rootCmd.Flags().StringVarP(&configFile, "config", "c", "../../.env", "Path to the configuration file")

	if err := rootCmd.Execute(); err != nil {
		log.Fatalf("Error executing command: %v", err)
	}
}

func runIndexer(cmd *cobra.Command, args []string) {
	// Load environment variables
	if err := godotenv.Load(configFile); err != nil {
		log.Printf("Warning: .env file not found at %s, using system environment variables", configFile)
	}

	// Initialize configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	// Initialize parsers
	parserManager := parsers.NewParserManager()

	// Initialize indexer
	idx := indexer.NewIndexer(db, cfg)

	// Process documents
	ctx := context.Background()
	if err := processDirectory(ctx, dataDir, parserManager, idx); err != nil {
		log.Fatalf("Failed to process directory: %v", err)
	}

	log.Println("Indexing completed successfully!")
}

func processDirectory(ctx context.Context, dir string, parserManager *parsers.ParserManager, idx *indexer.Indexer) error {
	return filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		// Get file extension
		ext := filepath.Ext(path)
		
		// Check if we can parse this file type
		if !parserManager.CanParse(ext) {
			log.Printf("Skipping unsupported file: %s", path)
			return nil
		}

		log.Printf("Processing file: %s", path)

		// Parse the document
		content, err := parserManager.Parse(path)
		if err != nil {
			log.Printf("Error parsing file %s: %v", path, err)
			return nil // Continue with other files
		}

		// Index the document
		if err := idx.IndexDocument(ctx, content, path); err != nil {
			log.Printf("Error indexing file %s: %v", path, err)
			return nil // Continue with other files
		}

		log.Printf("Successfully indexed: %s", path)
		return nil
	})
}
