package services

import (
	"context"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	"ai-customer-service/backend/internal/config"
	"ai-customer-service/backend/internal/database"
	"ai-customer-service/backend/internal/models"
)

type ChatService struct {
	db        *database.DB
	aiService *AIService
	config    *config.Config
	memoryFile string
}

func NewChatService(db *database.DB, aiService *AIService, cfg *config.Config) *ChatService {
	return &ChatService{
		db:         db,
		aiService:  aiService,
		config:     cfg,
		memoryFile: "memory.txt",
	}
}

func (s *ChatService) ProcessMessage(ctx context.Context, message string, model string) (string, error) {
	// Read existing memory
	memory, err := s.readMemory()
	if err != nil {
		log.Printf("Warning: failed to read memory: %v", err)
		memory = ""
	}

	// Prepare context with memory
	var contextualPrompt string
	if memory != "" {
		contextualPrompt = fmt.Sprintf("對話歷史:\n%s\n\n當前問題: %s", memory, message)
	} else {
		contextualPrompt = message
	}

	// Check for RAG context
	ragContext, err := s.getRAGContext(ctx, message)
	if err != nil {
		log.Printf("Warning: failed to get RAG context: %v", err)
	}

	// Add RAG context if available
	if ragContext != "" {
		contextualPrompt = fmt.Sprintf("參考資料:\n%s\n\n%s", ragContext, contextualPrompt)
	}

	// Generate AI response
	response, err := s.aiService.GenerateResponse(ctx, contextualPrompt, model)
	if err != nil {
		return "", fmt.Errorf("failed to generate AI response: %w", err)
	}

	// Save to memory file
	if err := s.appendToMemory(message, response); err != nil {
		log.Printf("Warning: failed to save to memory: %v", err)
	}

	// Save to database
	if err := s.saveToDatabase(ctx, message, response); err != nil {
		log.Printf("Warning: failed to save to database: %v", err)
	}

	return response, nil
}

func (s *ChatService) ClearMemory() error {
	// Clear memory file
	if err := os.Remove(s.memoryFile); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to remove memory file: %w", err)
	}

	// Clear database history (optional - you might want to keep this)
	// ctx := context.Background()
	// _, err := s.db.Pool.Exec(ctx, "DELETE FROM conversation_history")
	// if err != nil {
	//     return fmt.Errorf("failed to clear database history: %w", err)
	// }

	return nil
}

func (s *ChatService) GetHistory(ctx context.Context) ([]models.ConversationHistory, error) {
	query := `
		SELECT id, user_message, ai_response, timestamp, session_id 
		FROM conversation_history 
		ORDER BY timestamp ASC
	`

	rows, err := s.db.Pool.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query conversation history: %w", err)
	}
	defer rows.Close()

	var history []models.ConversationHistory
	for rows.Next() {
		var record models.ConversationHistory
		err := rows.Scan(
			&record.ID,
			&record.UserMessage,
			&record.AIResponse,
			&record.Timestamp,
			&record.SessionID,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan conversation record: %w", err)
		}
		history = append(history, record)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating conversation history: %w", err)
	}

	return history, nil
}

func (s *ChatService) readMemory() (string, error) {
	if _, err := os.Stat(s.memoryFile); os.IsNotExist(err) {
		return "", nil
	}

	data, err := ioutil.ReadFile(s.memoryFile)
	if err != nil {
		return "", err
	}

	return string(data), nil
}

func (s *ChatService) appendToMemory(userMessage, aiResponse string) error {
	file, err := os.OpenFile(s.memoryFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	defer file.Close()

	entry := fmt.Sprintf("用戶: %s\nAI: %s\n\n", userMessage, aiResponse)
	_, err = file.WriteString(entry)
	return err
}

func (s *ChatService) saveToDatabase(ctx context.Context, userMessage, aiResponse string) error {
	query := `
		INSERT INTO conversation_history (user_message, ai_response, session_id) 
		VALUES ($1, $2, $3)
	`

	_, err := s.db.Pool.Exec(ctx, query, userMessage, aiResponse, "default")
	return err
}

func (s *ChatService) getRAGContext(ctx context.Context, query string) (string, error) {
	// Generate embedding for the query
	embedding, err := s.aiService.GenerateEmbedding(ctx, query)
	if err != nil {
		return "", fmt.Errorf("failed to generate query embedding: %w", err)
	}

	// Convert embedding to PostgreSQL array format
	embeddingStr := fmt.Sprintf("[%s]", strings.Trim(strings.Join(strings.Fields(fmt.Sprintf("%v", embedding)), ","), "[]"))

	// Perform similarity search
	searchQuery := `
		SELECT content, (embedding <=> $1::vector) as distance
		FROM rag_documents 
		WHERE embedding IS NOT NULL
		ORDER BY embedding <=> $1::vector
		LIMIT 3
	`

	rows, err := s.db.Pool.Query(ctx, searchQuery, embeddingStr)
	if err != nil {
		return "", fmt.Errorf("failed to perform RAG search: %w", err)
	}
	defer rows.Close()

	var contexts []string
	for rows.Next() {
		var content string
		var distance float64
		if err := rows.Scan(&content, &distance); err != nil {
			continue // Skip problematic rows
		}
		
		// Only include results with reasonable similarity (distance < 0.8)
		if distance < 0.8 {
			contexts = append(contexts, content)
		}
	}

	if len(contexts) == 0 {
		return "", nil
	}

	return strings.Join(contexts, "\n\n"), nil
}
