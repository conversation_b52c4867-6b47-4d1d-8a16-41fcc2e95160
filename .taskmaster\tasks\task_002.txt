# Task ID: 2
# Title: Backend Server and PostgreSQL Database Setup
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Initialize the Golang backend server, establish a connection to the PostgreSQL database, and set up the necessary tables and the pgvector extension.
# Details:
In the `backend/` directory, set up a new Go project. Use a library like `net/http` for the web server and `jackc/pgx` for PostgreSQL connection. Write a startup script to connect to the database using the `DATABASE_URL` from the .env file. Execute SQL commands to enable the `pgvector` extension (`CREATE EXTENSION IF NOT EXISTS vector;`) and create tables for `conversation_history` (id, user_message, ai_response, timestamp) and `rag_documents` (id, content, embedding vector(1536)).

# Test Strategy:
Run the backend server and verify it connects to the PostgreSQL database without errors. Use a database client (e.g., psql, DBeaver) to confirm that the `vector` extension is enabled and the required tables (`conversation_history`, `rag_documents`) have been created with the correct schemas.
