-- Initialize database for AI Customer Service System

-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create conversation_history table
CREATE TABLE IF NOT EXISTS conversation_history (
    id SERIAL PRIMARY KEY,
    user_message TEXT NOT NULL,
    ai_response TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    session_id VARCHAR(255) DEFAULT 'default'
);

-- Create rag_documents table
CREATE TABLE IF NOT EXISTS rag_documents (
    id SERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    embedding vector(1536),
    source_file VARCHAR(255),
    chunk_index INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for vector similarity search
CREATE INDEX IF NOT EXISTS rag_documents_embedding_idx 
ON rag_documents USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);

-- Create index for conversation history
CREATE INDEX IF NOT EXISTS conversation_history_timestamp_idx 
ON conversation_history (timestamp);

CREATE INDEX IF NOT EXISTS conversation_history_session_idx 
ON conversation_history (session_id);

-- Insert sample data (optional)
INSERT INTO conversation_history (user_message, ai_response, session_id) VALUES
('你好', '您好！歡迎來到美而美早餐店，有什麼可以為您服務的嗎？', 'sample'),
('請問有什麼推薦的早餐？', '我推薦我們的招牌蛋餅配奶茶，還有香噴噴的煎餃也很受歡迎！', 'sample')
ON CONFLICT DO NOTHING;
