#!/bin/bash

# RAG Document Indexing Script

echo "📚 Starting RAG Document Indexing"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please create it from .env.example"
    exit 1
fi

# Check if RAGData directory exists
if [ ! -d "RAGData" ]; then
    echo "❌ RAGData directory not found. Please create it and add your documents."
    exit 1
fi

# Check if there are any files to index
if [ -z "$(find RAGData -type f \( -name "*.txt" -o -name "*.pdf" -o -name "*.docx" -o -name "*.xlsx" \))" ]; then
    echo "⚠️  No supported documents found in RAGData directory."
    echo "Supported formats: TXT, PDF, DOCX, XLSX"
    exit 1
fi

echo "🔍 Found documents to index:"
find RAGData -type f \( -name "*.txt" -o -name "*.pdf" -o -name "*.docx" -o -name "*.xlsx" \) -exec basename {} \;

echo ""
echo "🚀 Starting indexing process..."

# Navigate to indexing tool directory
cd tools/indexing

# Install dependencies
echo "📦 Installing dependencies..."
go mod tidy

# Run the indexing tool
echo "⚙️  Running indexer..."
go run main.go --data ../../RAGData --config ../../.env

if [ $? -eq 0 ]; then
    echo "✅ Document indexing completed successfully!"
    echo "📊 Documents are now available for RAG queries"
else
    echo "❌ Document indexing failed. Please check the logs above."
    exit 1
fi

cd ../..
echo "🎉 RAG indexing process completed!"
