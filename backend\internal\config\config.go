package config

import (
	"os"
)

type Config struct {
	DatabaseURL                string
	Google<PERSON><PERSON><PERSON>ey              string
	OllamaAPIURL              string
	OllamaModel               string
	OllamaEmbeddingModelName  string
	GeminiModel               string
	SystemPrompt              string
	Port                      string
}

func Load() *Config {
	return &Config{
		DatabaseURL:               getEnv("DATABASE_URL", "postgresql://cicuser:pi@localhost:5432/taskmaster_db?sslmode=disable"),
		GoogleAPIKey:              getEnv("GOOGLE_API_KEY", ""),
		OllamaAPIURL:              getEnv("OLLAMA_API_URL", "http://localhost:11434"),
		OllamaModel:               getEnv("OLLAMA_MODEL", "llama3.2:3b"),
		OllamaEmbeddingModelName:  getEnv("OLLAMA_EMBEDDING_MODEL_NAME", "nomic-embed-text"),
		GeminiModel:               getEnv("GEMINI_MODEL", "gemini-2.5-pro"),
		SystemPrompt:              getEnv("SYSTEM_PROMPT", "你是美而美早餐店的AI客服人員，請用親切友善的語氣回答客戶問題。"),
		Port:                      getEnv("PORT", "8080"),
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
