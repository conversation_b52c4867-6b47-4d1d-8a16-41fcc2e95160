package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"ai-customer-service/backend/internal/config"

	"github.com/google/generative-ai-go/genai"
	"google.golang.org/api/option"
)

type AIService struct {
	config *config.Config
	client *genai.Client
}

type OllamaRequest struct {
	Model  string `json:"model"`
	Prompt string `json:"prompt"`
	Stream bool   `json:"stream"`
}

type OllamaResponse struct {
	Response string `json:"response"`
	Done     bool   `json:"done"`
}

type OllamaEmbeddingRequest struct {
	Model  string `json:"model"`
	Prompt string `json:"prompt"`
}

type OllamaEmbeddingResponse struct {
	Embedding []float32 `json:"embedding"`
}

func NewAIService(cfg *config.Config) *AIService {
	service := &AIService{
		config: cfg,
	}

	// Initialize Gemini client if API key is provided
	if cfg.GoogleAPIKey != "" {
		client, err := genai.NewClient(context.Background(), option.WithAPIKey(cfg.GoogleAPIKey))
		if err == nil {
			service.client = client
		}
	}

	return service
}

func (s *AIService) GenerateResponse(ctx context.Context, prompt string, model string) (string, error) {
	switch strings.ToLower(model) {
	case "gemini":
		return s.generateGeminiResponse(ctx, prompt)
	case "ollama":
		return s.generateOllamaResponse(ctx, prompt)
	default:
		// Default to Gemini if available, otherwise Ollama
		if s.client != nil {
			return s.generateGeminiResponse(ctx, prompt)
		}
		return s.generateOllamaResponse(ctx, prompt)
	}
}

func (s *AIService) generateGeminiResponse(ctx context.Context, prompt string) (string, error) {
	if s.client == nil {
		return "", fmt.Errorf("Gemini client not initialized - check GOOGLE_API_KEY")
	}

	model := s.client.GenerativeModel(s.config.GeminiModel)
	model.SetTemperature(0.7)
	model.SetMaxOutputTokens(1000)

	// Add system prompt
	fullPrompt := s.config.SystemPrompt + "\n\n用戶問題: " + prompt

	resp, err := model.GenerateContent(ctx, genai.Text(fullPrompt))
	if err != nil {
		return "", fmt.Errorf("failed to generate Gemini response: %w", err)
	}

	if len(resp.Candidates) == 0 || len(resp.Candidates[0].Content.Parts) == 0 {
		return "", fmt.Errorf("no response generated from Gemini")
	}

	// Extract text from the response
	var responseText strings.Builder
	for _, part := range resp.Candidates[0].Content.Parts {
		if textPart, ok := part.(genai.Text); ok {
			responseText.WriteString(string(textPart))
		}
	}

	return responseText.String(), nil
}

func (s *AIService) generateOllamaResponse(ctx context.Context, prompt string) (string, error) {
	// Add system prompt
	fullPrompt := s.config.SystemPrompt + "\n\n用戶問題: " + prompt

	reqBody := OllamaRequest{
		Model:  s.config.OllamaModel,
		Prompt: fullPrompt,
		Stream: false,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", s.config.OllamaAPIURL+"/api/generate", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request to Ollama: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("Ollama API error (status %d): %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	var ollamaResp OllamaResponse
	if err := json.Unmarshal(body, &ollamaResp); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return ollamaResp.Response, nil
}

func (s *AIService) GenerateEmbedding(ctx context.Context, text string) ([]float32, error) {
	reqBody := OllamaEmbeddingRequest{
		Model:  s.config.OllamaEmbeddingModelName,
		Prompt: text,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal embedding request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", s.config.OllamaAPIURL+"/api/embeddings", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create embedding request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send embedding request to Ollama: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Ollama embedding API error (status %d): %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read embedding response: %w", err)
	}

	var embeddingResp OllamaEmbeddingResponse
	if err := json.Unmarshal(body, &embeddingResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal embedding response: %w", err)
	}

	return embeddingResp.Embedding, nil
}

func (s *AIService) Close() {
	if s.client != nil {
		s.client.Close()
	}
}
