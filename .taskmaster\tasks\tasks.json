{"master": {"tasks": [{"id": 1, "title": "Project Scaffolding and Environment Setup", "description": "Set up the complete project structure, including directories for frontend, backend, tools, and data. Initialize environment variable management using a .env file as specified in the PRD.", "details": "Create the top-level directories: `frontend/`, `backend/`, `tools/`, `RAGData/`, `PRD/`. In the project root, create a `.env.example` file with placeholders for `DATABASE_URL`, `GOOGLE_API_KEY`, `OLLAMA_API_URL`, `OLLAMA_MODEL`, `OLLAMA_EMBEDDING_MODEL_NAME`, `GEMINI_MODEL`, and `SYSTEM_PROMPT`. The backend and frontend applications should be configured to load these variables. Initialize a Git repository and add a .gitignore file.", "testStrategy": "Verify that all specified directories are created. Confirm that both frontend and backend applications can correctly read variables from a local `.env` file. Check that the repository is initialized and sensitive files are ignored.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 2, "title": "Backend Server and PostgreSQL Database Setup", "description": "Initialize the Golang backend server, establish a connection to the PostgreSQL database, and set up the necessary tables and the pgvector extension.", "details": "In the `backend/` directory, set up a new Go project. Use a library like `net/http` for the web server and `jackc/pgx` for PostgreSQL connection. Write a startup script to connect to the database using the `DATABASE_URL` from the .env file. Execute SQL commands to enable the `pgvector` extension (`CREATE EXTENSION IF NOT EXISTS vector;`) and create tables for `conversation_history` (id, user_message, ai_response, timestamp) and `rag_documents` (id, content, embedding vector(1536)).", "testStrategy": "Run the backend server and verify it connects to the PostgreSQL database without errors. Use a database client (e.g., psql, DBeaver) to confirm that the `vector` extension is enabled and the required tables (`conversation_history`, `rag_documents`) have been created with the correct schemas.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 3, "title": "Frontend UI Scaffolding with Next.js and Tailwind CSS", "description": "Create the Next.js frontend application and build the static UI layout based on the PRD's requirements, including the header, conversation area, and voice control button.", "details": "Inside the `frontend/` directory, initialize a new Next.js application using `npx create-next-app@latest`. Integrate Tailwind CSS for styling. Create React components for the main layout: `Header.js`, `ConversationArea.js`, and `Controls.js`. The `Controls.js` component should feature a prominent, center-bottom button for 'Start/Stop Voice'. Use React Context for initial state management (e.g., `isListening`, `conversation`). The layout should be responsive.", "testStrategy": "Run the Next.js development server. Verify the UI matches the layout described in the PRD and the `UI.png` mockup. Test responsiveness by resizing the browser window to check mobile and desktop views. Ensure all static UI elements are present and styled correctly.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 4, "title": "Implement Real-Time Speech-to-Text (STT)", "description": "Implement real-time voice capture and transcription using the Web Speech API. This includes handling microphone permissions and displaying the transcribed text on the UI.", "details": "In the frontend, use the `SpeechRecognition` interface from the Web Speech API. On component mount or app start, request microphone permission. Implement the 'Start/Stop Voice' button logic to start and stop the recognition service. Set `continuous` to true for always-on input. As results are received, update the application state with the transcribed text and display it in the conversation area. Provide visual feedback (e.g., a pulsing icon) when the microphone is active.", "testStrategy": "Unit test the state changes for starting/stopping listening. Manually test in Chrome and Firefox. Verify the microphone permission prompt appears correctly. Speak into the microphone and confirm that the transcribed text appears in real-time on the screen. Test the 'Stop Voice' functionality.", "priority": "high", "dependencies": [3], "status": "pending", "subtasks": []}, {"id": 5, "title": "Backend LLM Integration and Conversation Memory", "description": "Create a backend API endpoint to receive user text, communicate with the Google Gemini LLM, and return the generated response. Also, implement logic to save and retrieve conversation context from a `memory.txt` file.", "details": "In the Go backend, create a RESTful endpoint, e.g., `POST /api/chat`. This endpoint will receive a JSON payload with the user's message. Read the existing context from `memory.txt`, prepend it to the user's message, and send the combined text to the Google Gemini API (`gemini-2.5-pro`) using the `GOOGLE_API_KEY`. After receiving the response, append the user message and AI response to `memory.txt`. Implement a `POST /api/chat/clear` endpoint to erase `memory.txt`.", "testStrategy": "Use an API client like <PERSON><PERSON> or <PERSON> to send requests to the `/api/chat` endpoint. Verify that a valid response is received from the Gemini API. Check the `memory.txt` file to ensure the conversation history is being appended correctly. Test the `/api/chat/clear` endpoint.", "priority": "high", "dependencies": [2], "status": "pending", "subtasks": []}, {"id": 6, "title": "Integrate Frontend/Backend and Implement Text-to-Speech (TTS)", "description": "Connect the frontend to the backend chat API and implement Text-to-Speech (TTS) to play back the AI's response, completing the core voice-to-voice interaction loop.", "details": "Modify the frontend STT logic (from Task 4) to send the final transcribed text to the backend `/api/chat` endpoint (from Task 5). Upon receiving the AI's text response, use the `SpeechSynthesis` interface of the Web Speech API to convert the text to audio and play it. Use `speechSynthesis.speak()` with a `SpeechSynthesisUtterance` object. Implement logic to interrupt the current speech playback if the user starts speaking again.", "testStrategy": "Perform an end-to-end test: 1. Speak a query. 2. Verify it's transcribed and sent to the backend. 3. Verify the backend returns an AI response. 4. Verify the frontend speaks the response aloud. Test the interrupt functionality by speaking while the AI is responding.", "priority": "high", "dependencies": [4, 5], "status": "pending", "subtasks": []}, {"id": 7, "title": "Implement and Display Conversation History", "description": "Implement the conversation history feature by storing each exchange in the PostgreSQL database and displaying it in a scrollable UI on the frontend.", "details": "Modify the backend `/api/chat` endpoint to save the user's message and the AI's response to the `conversation_history` table in PostgreSQL, along with a timestamp. Create a new backend endpoint, `GET /api/history`, to retrieve all conversation records. On the frontend, fetch data from this endpoint when the app loads and display it in the `ConversationArea.js` component. Ensure the area is scrollable.", "testStrategy": "After a few conversation turns, reload the application and verify that the previous conversation is loaded and displayed correctly. Check the `conversation_history` table in the database to confirm records are being inserted accurately. Test the scroll functionality of the chat view.", "priority": "medium", "dependencies": [6], "status": "pending", "subtasks": []}, {"id": 8, "title": "Develop RAG Offline Indexing CLI Tool", "description": "Develop a Go-based CLI tool for offline document indexing for the RAG system. This tool will parse documents, generate embeddings, and store them in the PostgreSQL vector database.", "details": "In the `tools/indexing` directory, create a new Go CLI application using a library like `cobra`. The tool should accept a path to the `RAGData` directory. Implement parsers for PDF (`unidoc`), TXT (`os.ReadFile`), DOCX (`nguyenthenguyen/docx`), and XLSX (`xuri/excelize`). Use a recursive character splitter to chunk text. For each chunk, call the specified embedding model API (Ollama or Gemini) to get a vector. Connect to the PostgreSQL database and insert the content chunk and its vector into the `rag_documents` table.", "testStrategy": "Place sample documents (PDF, TXT, DOCX, XLSX) in the `RAGData` folder. Run the CLI tool. Verify the tool runs without errors. Inspect the `rag_documents` table in PostgreSQL to confirm that document chunks and their corresponding vectors have been successfully stored.", "priority": "medium", "dependencies": [2], "status": "pending", "subtasks": []}, {"id": 9, "title": "Integrate RAG into AI Response Generation", "description": "Integrate the RAG functionality into the backend chat endpoint to provide context-aware answers based on the indexed documents.", "details": "Modify the `/api/chat` endpoint logic. When a user query is received, first generate an embedding for the query using the same embedding model as the indexing tool. Perform a similarity search (e.g., cosine distance) against the vectors in the `rag_documents` table using `pgvector`'s `<=>` operator. Retrieve the top 3-5 most relevant document chunks. Prepend this retrieved context to the prompt sent to the Gemini LLM, instructing it to use the context to answer the user's question.", "testStrategy": "Index a document with specific, non-public information. Ask a question that can only be answered using the information from that document. Verify that the AI's response is accurate and based on the provided context. Check backend logs to ensure the similarity search is being performed.", "priority": "medium", "dependencies": [5, 8], "status": "pending", "subtasks": []}, {"id": 10, "title": "Implement MCP Server for Extensible Tool Use", "description": "Create an independent Go microservice for the MCP (Model Context Protocol) server that exposes external tools like getting the current time and performing a web search.", "details": "Create a new Go service, potentially in `backend/mcp_server/`. This service will run independently. Implement two endpoints: `GET /tools` which returns a JSON schema of available tools (e.g., `{\"name\": \"getCurrentTime\", \"description\": \"...\"}`), and `POST /execute` which accepts a tool name and arguments. Implement the logic for a `getCurrentTime` tool and a `webSearch` tool (using a simple HTTP GET request to a search engine). Modify the main backend's LLM call to include the tool schemas and logic to call the MCP server's `/execute` endpoint when the LLM decides to use a tool.", "testStrategy": "Start the MCP server. Use an API client to test the `/tools` and `/execute` endpoints directly. Then, perform an end-to-end test by asking the AI a question like 'What time is it?' or 'Search the web for Taipei 101'. Verify the main backend calls the MCP server and the final response incorporates the tool's output.", "priority": "low", "dependencies": [5], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-07-05T06:02:28.966Z", "updated": "2025-07-05T06:02:28.966Z", "description": "Tasks for master context"}}}