# Task ID: 1
# Title: Project Scaffolding and Environment Setup
# Status: pending
# Dependencies: None
# Priority: high
# Description: Set up the complete project structure, including directories for frontend, backend, tools, and data. Initialize environment variable management using a .env file as specified in the PRD.
# Details:
Create the top-level directories: `frontend/`, `backend/`, `tools/`, `RAGData/`, `PRD/`. In the project root, create a `.env.example` file with placeholders for `DATABASE_URL`, `GOOGLE_API_KEY`, `OLLAMA_API_URL`, `OLLAMA_MODEL`, `OLLAMA_EMBEDDING_MODEL_NAME`, `GEMINI_MODEL`, and `SYSTEM_PROMPT`. The backend and frontend applications should be configured to load these variables. Initialize a Git repository and add a .gitignore file.

# Test Strategy:
Verify that all specified directories are created. Confirm that both frontend and backend applications can correctly read variables from a local `.env` file. Check that the repository is initialized and sensitive files are ignored.
