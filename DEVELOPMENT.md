# Development Guide

This guide provides detailed instructions for developing and maintaining the AI Customer Service System.

## 🏗️ Architecture Overview

The system consists of four main components:

1. **Frontend** (Next.js + React + TypeScript)
   - Voice-to-voice interface
   - Real-time conversation display
   - Speech recognition and synthesis

2. **Backend** (Go + PostgreSQL)
   - REST API server
   - AI model integration (Gemini/Ollama)
   - Conversation history and RAG

3. **MCP Server** (Go)
   - External tool integration
   - Extensible microservice architecture

4. **RAG Indexing Tool** (Go CLI)
   - Document processing and embedding
   - Vector database population

## 🚀 Quick Start

### Option 1: Docker (Recommended)
```bash
# Start with Docker
./scripts/docker-start.sh

# Monitor system
./scripts/monitor-system.sh

# Run tests
./scripts/run-tests.sh
```

### Option 2: Local Development
```bash
# Setup environment
cp .env.example .env
# Edit .env with your configuration

# Test setup
./scripts/test-setup.sh

# Start development servers
./scripts/start-dev.sh
```

## 📁 Project Structure

```
├── frontend/                 # Next.js application
│   ├── src/
│   │   ├── app/             # App router pages
│   │   ├── components/      # React components
│   │   └── types/           # TypeScript definitions
│   ├── public/              # Static assets
│   └── package.json
│
├── backend/                  # Go API server
│   ├── internal/
│   │   ├── config/          # Configuration
│   │   ├── database/        # Database layer
│   │   ├── handlers/        # HTTP handlers
│   │   ├── models/          # Data models
│   │   └── services/        # Business logic
│   ├── main.go              # Entry point
│   └── go.mod
│
├── backend/mcp_server/       # MCP microservice
│   ├── internal/
│   │   ├── handlers/        # HTTP handlers
│   │   ├── models/          # Data models
│   │   └── tools/           # Tool implementations
│   ├── main.go
│   └── go.mod
│
├── tools/indexing/           # RAG indexing CLI
│   ├── internal/
│   │   ├── config/          # Configuration
│   │   ├── database/        # Database operations
│   │   ├── indexer/         # Indexing logic
│   │   └── parsers/         # Document parsers
│   ├── main.go
│   └── go.mod
│
├── RAGData/                  # Document storage
├── scripts/                  # Utility scripts
└── .taskmaster/             # Task management
```

## 🔧 Development Workflow

### 1. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Required variables:
DATABASE_URL="postgresql://cicuser:pi@localhost:5432/taskmaster_db?sslmode=disable"
GOOGLE_API_KEY="your_google_api_key"
OLLAMA_API_URL="http://localhost:11434"
SYSTEM_PROMPT="你是美而美早餐店的AI客服人員..."
```

### 2. Database Setup

```bash
# Using Docker
docker-compose up -d postgres

# Or install PostgreSQL locally with pgvector extension
# CREATE DATABASE taskmaster_db;
# CREATE EXTENSION vector;
```

### 3. Backend Development

```bash
cd backend

# Install dependencies
go mod tidy

# Run development server
go run main.go

# Run tests
go test ./...

# Build for production
go build -o server main.go
```

### 4. Frontend Development

```bash
cd frontend

# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Run tests
npm test
```

### 5. MCP Server Development

```bash
cd backend/mcp_server

# Install dependencies
go mod tidy

# Run development server
go run main.go

# Add new tools in internal/tools/
```

### 6. RAG Document Indexing

```bash
# Add documents to RAGData/
cp your_documents.pdf RAGData/

# Run indexing
cd tools/indexing
go run main.go --data ../../RAGData

# Or use the script
./scripts/index-documents.sh
```

## 🧪 Testing

### Automated Testing
```bash
# Run comprehensive test suite
./scripts/run-tests.sh

# Test individual components
cd backend && go test ./...
cd frontend && npm test
```

### Manual Testing
```bash
# Health checks
curl http://localhost:8080/api/v1/health
curl http://localhost:8081/health
curl http://localhost:3000/api/health

# Chat API test
curl -X POST http://localhost:8080/api/v1/chat \
  -H "Content-Type: application/json" \
  -d '{"message":"你好","model":"Gemini"}'

# MCP tools test
curl http://localhost:8081/tools
curl -X POST http://localhost:8081/execute \
  -H "Content-Type: application/json" \
  -d '{"name":"getCurrentTime","arguments":{}}'
```

## 📊 Monitoring

### System Monitoring
```bash
# Real-time system status
./scripts/monitor-system.sh

# Docker container logs
docker-compose logs -f [service_name]

# Database monitoring
./scripts/backup-db.sh
```

### Performance Metrics
- Response time monitoring
- Database query performance
- Memory and CPU usage
- Error rate tracking

## 🔒 Security

### API Security
- CORS configuration
- Input validation
- Rate limiting (TODO)
- Authentication (TODO)

### Database Security
- Connection encryption
- User permissions
- Backup encryption
- Access logging

## 🚀 Deployment

### Docker Deployment
```bash
# Production deployment
./scripts/docker-start.sh

# Scale services
docker-compose up -d --scale backend=3

# Update services
docker-compose pull
docker-compose up -d
```

### Manual Deployment
```bash
# Build all components
cd frontend && npm run build
cd ../backend && go build -o server main.go
cd mcp_server && go build -o mcp_server main.go
cd ../../tools/indexing && go build -o indexer main.go

# Deploy to server
# Configure reverse proxy (nginx/apache)
# Set up systemd services
# Configure monitoring
```

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check PostgreSQL status
   docker-compose ps postgres
   # Check connection string in .env
   ```

2. **AI API Errors**
   ```bash
   # Verify API keys in .env
   # Check network connectivity
   # Review service logs
   ```

3. **Frontend Build Errors**
   ```bash
   # Clear cache
   rm -rf .next node_modules
   npm install
   npm run build
   ```

4. **Voice Recognition Not Working**
   - Check browser permissions
   - Ensure HTTPS in production
   - Verify microphone access

### Debug Mode
```bash
# Enable debug logging
export DEBUG=true

# Verbose logging
docker-compose logs -f --tail=100 [service]
```

## 📈 Performance Optimization

### Backend Optimization
- Database query optimization
- Connection pooling
- Caching strategies
- Load balancing

### Frontend Optimization
- Code splitting
- Image optimization
- Bundle analysis
- Service worker caching

### Database Optimization
- Index optimization
- Query performance tuning
- Connection pooling
- Vector search optimization

## 🔄 Continuous Integration

### GitHub Actions (TODO)
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run tests
        run: ./scripts/run-tests.sh
```

### Pre-commit Hooks
```bash
# Install pre-commit hooks
npm install husky --save-dev
npx husky install
npx husky add .husky/pre-commit "npm test"
```

## 📚 Additional Resources

- [Go Documentation](https://golang.org/doc/)
- [Next.js Documentation](https://nextjs.org/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [pgvector Documentation](https://github.com/pgvector/pgvector)
- [Google Gemini API](https://ai.google.dev/docs)
- [Ollama Documentation](https://ollama.ai/docs)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `./scripts/run-tests.sh`
5. Submit a pull request

## 📞 Support

For technical support or questions:
- Check the troubleshooting section
- Review system logs
- Run the test suite
- Create an issue with detailed information
