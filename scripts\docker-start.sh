#!/bin/bash

# Docker Deployment Script for AI Customer Service System

echo "🐳 Starting AI Customer Service System with Docker"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Copying from .env.example..."
    cp .env.example .env
    echo "📝 Please edit .env file with your actual configuration values"
    echo "🔑 Important: Set your GOOGLE_API_KEY and other API keys"
    read -p "Press Enter to continue after editing .env file..."
fi

# Create necessary directories
mkdir -p logs
mkdir -p data/postgres
mkdir -p data/ollama

echo "🔧 Building Docker images..."
docker-compose build

echo "🚀 Starting services..."

# Start core services (without Ollama by default)
docker-compose up -d postgres backend mcp_server frontend

echo "⏳ Waiting for services to be ready..."
sleep 10

# Check service health
echo "🔍 Checking service health..."

# Check PostgreSQL
if docker-compose exec postgres pg_isready -U cicuser -d taskmaster_db > /dev/null 2>&1; then
    echo "✅ PostgreSQL is ready"
else
    echo "❌ PostgreSQL is not ready"
fi

# Check Backend
if curl -s http://localhost:8080/api/v1/health > /dev/null 2>&1; then
    echo "✅ Backend API is ready"
else
    echo "❌ Backend API is not ready"
fi

# Check MCP Server
if curl -s http://localhost:8081/health > /dev/null 2>&1; then
    echo "✅ MCP Server is ready"
else
    echo "❌ MCP Server is not ready"
fi

# Check Frontend
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Frontend is ready"
else
    echo "❌ Frontend is not ready"
fi

echo ""
echo "🎉 AI Customer Service System is running!"
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8080"
echo "🛠️  MCP Server: http://localhost:8081"
echo "🗄️  PostgreSQL: localhost:5432"
echo ""
echo "📊 To view logs: docker-compose logs -f [service_name]"
echo "🛑 To stop: docker-compose down"
echo "🔄 To restart: docker-compose restart [service_name]"
echo ""

# Optional: Start Ollama
read -p "🤖 Do you want to start Ollama for local AI models? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 Starting Ollama..."
    docker-compose --profile ollama up -d ollama
    echo "⏳ Waiting for Ollama to be ready..."
    sleep 15
    
    echo "📥 Pulling default models..."
    docker-compose exec ollama ollama pull llama3.2:3b
    docker-compose exec ollama ollama pull nomic-embed-text
    
    echo "✅ Ollama is ready with models!"
fi

echo "🎊 Setup complete! Happy coding!"
