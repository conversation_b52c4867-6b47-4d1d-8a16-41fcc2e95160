package database

import (
	"context"
	"fmt"
	"log"

	"github.com/jackc/pgx/v5/pgxpool"
)

type DB struct {
	Pool *pgxpool.Pool
}

func Initialize(databaseURL string) (*DB, error) {
	// Create connection pool
	pool, err := pgxpool.New(context.Background(), databaseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	// Test connection
	if err := pool.Ping(context.Background()); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	db := &DB{Pool: pool}

	// Initialize database schema
	if err := db.initializeSchema(); err != nil {
		return nil, fmt.E<PERSON><PERSON>("failed to initialize schema: %w", err)
	}

	log.Println("Database initialized successfully")
	return db, nil
}

func (db *DB) Close() {
	if db.Pool != nil {
		db.Pool.Close()
	}
}

func (db *DB) initializeSchema() error {
	ctx := context.Background()

	// Enable pgvector extension
	_, err := db.Pool.Exec(ctx, "CREATE EXTENSION IF NOT EXISTS vector;")
	if err != nil {
		return fmt.Errorf("failed to create vector extension: %w", err)
	}

	// Create conversation_history table
	conversationHistorySQL := `
		CREATE TABLE IF NOT EXISTS conversation_history (
			id SERIAL PRIMARY KEY,
			user_message TEXT NOT NULL,
			ai_response TEXT NOT NULL,
			timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
			session_id VARCHAR(255) DEFAULT 'default'
		);
	`
	_, err = db.Pool.Exec(ctx, conversationHistorySQL)
	if err != nil {
		return fmt.Errorf("failed to create conversation_history table: %w", err)
	}

	// Create rag_documents table
	ragDocumentsSQL := `
		CREATE TABLE IF NOT EXISTS rag_documents (
			id SERIAL PRIMARY KEY,
			content TEXT NOT NULL,
			embedding vector(1536),
			source_file VARCHAR(255),
			chunk_index INTEGER DEFAULT 0,
			created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
		);
	`
	_, err = db.Pool.Exec(ctx, ragDocumentsSQL)
	if err != nil {
		return fmt.Errorf("failed to create rag_documents table: %w", err)
	}

	// Create index for vector similarity search
	indexSQL := `
		CREATE INDEX IF NOT EXISTS rag_documents_embedding_idx 
		ON rag_documents USING ivfflat (embedding vector_cosine_ops) 
		WITH (lists = 100);
	`
	_, err = db.Pool.Exec(ctx, indexSQL)
	if err != nil {
		log.Printf("Warning: failed to create vector index (this is normal if pgvector is not properly configured): %v", err)
	}

	log.Println("Database schema initialized successfully")
	return nil
}
