package parsers

import (
	"fmt"
	"strings"

	"github.com/xuri/excelize/v2"
)

type XlsxParser struct{}

func (p *XlsxParser) Parse(filePath string) (string, error) {
	// Open the Excel file
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open XLSX file: %w", err)
	}
	defer f.Close()

	var content strings.Builder

	// Get all sheet names
	sheets := f.GetSheetList()

	for _, sheetName := range sheets {
		content.WriteString(fmt.Sprintf("Sheet: %s\n", sheetName))

		// Get all rows in the sheet
		rows, err := f.GetRows(sheetName)
		if err != nil {
			continue // Skip problematic sheets
		}

		for _, row := range rows {
			// Join all cells in the row
			rowContent := strings.Join(row, " | ")
			if strings.TrimSpace(rowContent) != "" {
				content.WriteString(rowContent)
				content.WriteString("\n")
			}
		}

		content.WriteString("\n")
	}

	return content.String(), nil
}
