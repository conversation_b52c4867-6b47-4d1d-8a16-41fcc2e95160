package indexer

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"ai-customer-service/tools/indexing/internal/config"
	"ai-customer-service/tools/indexing/internal/database"
)

type Indexer struct {
	db     *database.DB
	config *config.Config
}

type OllamaEmbeddingRequest struct {
	Model  string `json:"model"`
	Prompt string `json:"prompt"`
}

type OllamaEmbeddingResponse struct {
	Embedding []float32 `json:"embedding"`
}

func NewIndexer(db *database.DB, cfg *config.Config) *Indexer {
	return &Indexer{
		db:     db,
		config: cfg,
	}
}

func (idx *Indexer) IndexDocument(ctx context.Context, content string, sourceFile string) error {
	// Clear existing documents from this source
	if err := idx.db.ClearDocumentsBySource(ctx, sourceFile); err != nil {
		return fmt.Errorf("failed to clear existing documents: %w", err)
	}

	// Split content into chunks
	chunks := idx.splitIntoChunks(content)

	// Process each chunk
	for i, chunk := range chunks {
		// Generate embedding
		embedding, err := idx.generateEmbedding(ctx, chunk)
		if err != nil {
			return fmt.Errorf("failed to generate embedding for chunk %d: %w", i, err)
		}

		// Store in database
		if err := idx.db.InsertDocument(ctx, chunk, embedding, sourceFile, i); err != nil {
			return fmt.Errorf("failed to store chunk %d: %w", i, err)
		}
	}

	return nil
}

func (idx *Indexer) splitIntoChunks(content string) []string {
	// Simple character-based splitting with overlap
	var chunks []string
	
	// Clean the content
	content = strings.TrimSpace(content)
	if len(content) == 0 {
		return chunks
	}

	chunkSize := idx.config.ChunkSize
	overlap := idx.config.ChunkOverlap

	for i := 0; i < len(content); i += chunkSize - overlap {
		end := i + chunkSize
		if end > len(content) {
			end = len(content)
		}

		chunk := content[i:end]
		chunk = strings.TrimSpace(chunk)
		
		if len(chunk) > 0 {
			chunks = append(chunks, chunk)
		}

		// If we've reached the end, break
		if end == len(content) {
			break
		}
	}

	return chunks
}

func (idx *Indexer) generateEmbedding(ctx context.Context, text string) ([]float32, error) {
	reqBody := OllamaEmbeddingRequest{
		Model:  idx.config.OllamaEmbeddingModelName,
		Prompt: text,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal embedding request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", idx.config.OllamaAPIURL+"/api/embeddings", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create embedding request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send embedding request to Ollama: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Ollama embedding API error (status %d): %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read embedding response: %w", err)
	}

	var embeddingResp OllamaEmbeddingResponse
	if err := json.Unmarshal(body, &embeddingResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal embedding response: %w", err)
	}

	return embeddingResp.Embedding, nil
}
