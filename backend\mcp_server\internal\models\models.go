package models

// ToolSchema represents the schema of a tool
type ToolSchema struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters,omitempty"`
}

// ToolsResponse represents the response for GET /tools
type ToolsResponse struct {
	Tools []ToolSchema `json:"tools"`
}

// ExecuteRequest represents the request for POST /execute
type ExecuteRequest struct {
	Name      string                 `json:"name"`
	Arguments map[string]interface{} `json:"arguments,omitempty"`
}

// ExecuteResponse represents the response for POST /execute
type ExecuteResponse struct {
	Result string `json:"result,omitempty"`
	Error  string `json:"error,omitempty"`
}

// HealthResponse represents the health check response
type HealthResponse struct {
	Status    string `json:"status"`
	Timestamp string `json:"timestamp"`
	Version   string `json:"version"`
}
