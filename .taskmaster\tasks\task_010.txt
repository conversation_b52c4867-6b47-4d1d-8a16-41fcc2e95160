# Task ID: 10
# Title: Implement MCP Server for Extensible Tool Use
# Status: pending
# Dependencies: 5
# Priority: low
# Description: Create an independent Go microservice for the MCP (Model Context Protocol) server that exposes external tools like getting the current time and performing a web search.
# Details:
Create a new Go service, potentially in `backend/mcp_server/`. This service will run independently. Implement two endpoints: `GET /tools` which returns a JSON schema of available tools (e.g., `{"name": "getCurrentTime", "description": "..."}`), and `POST /execute` which accepts a tool name and arguments. Implement the logic for a `getCurrentTime` tool and a `webSearch` tool (using a simple HTTP GET request to a search engine). Modify the main backend's LLM call to include the tool schemas and logic to call the MCP server's `/execute` endpoint when the LLM decides to use a tool.

# Test Strategy:
Start the MCP server. Use an API client to test the `/tools` and `/execute` endpoints directly. Then, perform an end-to-end test by asking the AI a question like 'What time is it?' or 'Search the web for Taipei 101'. Verify the main backend calls the MCP server and the final response incorporates the tool's output.
