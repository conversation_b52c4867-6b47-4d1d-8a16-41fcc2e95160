package parsers

import (
	"fmt"
	"strings"
)

type ParserManager struct {
	parsers map[string]Parser
}

type Parser interface {
	Parse(filePath string) (string, error)
}

func NewParserManager() *ParserManager {
	pm := &ParserManager{
		parsers: make(map[string]Parser),
	}

	// Register parsers
	pm.parsers[".txt"] = &TxtParser{}
	pm.parsers[".pdf"] = &PdfParser{}
	pm.parsers[".docx"] = &DocxParser{}
	pm.parsers[".xlsx"] = &XlsxParser{}

	return pm
}

func (pm *ParserManager) CanParse(extension string) bool {
	_, exists := pm.parsers[strings.ToLower(extension)]
	return exists
}

func (pm *ParserManager) Parse(filePath string) (string, error) {
	extension := strings.ToLower(getFileExtension(filePath))
	parser, exists := pm.parsers[extension]
	if !exists {
		return "", fmt.<PERSON>rrorf("no parser available for extension: %s", extension)
	}

	return parser.Parse(filePath)
}

func getFileExtension(filePath string) string {
	parts := strings.Split(filePath, ".")
	if len(parts) < 2 {
		return ""
	}
	return "." + parts[len(parts)-1]
}
