# Task ID: 7
# Title: Implement and Display Conversation History
# Status: pending
# Dependencies: 6
# Priority: medium
# Description: Implement the conversation history feature by storing each exchange in the PostgreSQL database and displaying it in a scrollable UI on the frontend.
# Details:
Modify the backend `/api/chat` endpoint to save the user's message and the AI's response to the `conversation_history` table in PostgreSQL, along with a timestamp. Create a new backend endpoint, `GET /api/history`, to retrieve all conversation records. On the frontend, fetch data from this endpoint when the app loads and display it in the `ConversationArea.js` component. Ensure the area is scrollable.

# Test Strategy:
After a few conversation turns, reload the application and verify that the previous conversation is loaded and displayed correctly. Check the `conversation_history` table in the database to confirm records are being inserted accurately. Test the scroll functionality of the chat view.
