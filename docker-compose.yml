version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: pgvector/pgvector:pg16
    container_name: ai-customer-service-db
    environment:
      POSTGRES_DB: taskmaster_db
      POSTGRES_USER: cicuser
      POSTGRES_PASSWORD: pi
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cicuser -d taskmaster_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ai-customer-service

  # Backend API Server
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: ai-customer-service-backend
    environment:
      - DATABASE_URL=*************************************/taskmaster_db?sslmode=disable
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - OLLAMA_API_URL=${OLLAMA_API_URL:-http://host.docker.internal:11434}
      - OLLAMA_MODEL=${OLLAMA_MODEL:-llama3.2:3b}
      - OLLAMA_EMBEDDING_MODEL_NAME=${OLLAMA_EMBEDDING_MODEL_NAME:-nomic-embed-text}
      - GEMINI_MODEL=${GEMINI_MODEL:-gemini-2.5-pro}
      - SYSTEM_PROMPT=${SYSTEM_PROMPT:-你是美而美早餐店的AI客服人員，請用親切友善的語氣回答客戶問題。}
      - PORT=8080
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - ai-customer-service
    restart: unless-stopped

  # MCP Server
  mcp_server:
    build:
      context: .
      dockerfile: Dockerfile.mcp
    container_name: ai-customer-service-mcp
    ports:
      - "8081:8081"
    networks:
      - ai-customer-service
    restart: unless-stopped

  # Frontend Application
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: ai-customer-service-frontend
    environment:
      - BACKEND_URL=http://backend:8080
      - NEXT_PUBLIC_BACKEND_URL=http://localhost:8080
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - ai-customer-service
    restart: unless-stopped

  # Ollama (Optional - for local AI models)
  ollama:
    image: ollama/ollama:latest
    container_name: ai-customer-service-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - ai-customer-service
    restart: unless-stopped
    profiles:
      - ollama

volumes:
  postgres_data:
    driver: local
  ollama_data:
    driver: local

networks:
  ai-customer-service:
    driver: bridge
