@echo off
echo 🧪 測試 AI 客服系統

echo 🔍 檢查前端服務...
curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 前端服務正常
) else (
    echo ❌ 前端服務無法訪問
)

echo 🔍 檢查後端 API...
curl -s http://localhost:8080/api/v1/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 後端 API 正常
) else (
    echo ❌ 後端 API 無法訪問
)

echo 🔍 檢查 MCP 服務器...
curl -s http://localhost:8081/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ MCP 服務器正常
) else (
    echo ❌ MCP 服務器無法訪問
)

echo.
echo 🧪 測試聊天 API...
curl -s -X POST http://localhost:8080/api/v1/chat -H "Content-Type: application/json" -d "{\"message\":\"你好\",\"model\":\"Gemini\"}" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 聊天 API 正常
) else (
    echo ❌ 聊天 API 測試失敗
)

echo.
echo 📋 系統狀態摘要:
echo 🌐 前端: http://localhost:3000
echo 🔧 後端: http://localhost:8080
echo 🛠️  MCP: http://localhost:8081
echo.
pause
