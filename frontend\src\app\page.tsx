'use client';

import { useState, useEffect, useRef } from 'react';
import ConversationView from '@/components/ConversationView';

// 定義對話訊息的結構
interface Message {
  role: 'user' | 'model';
  content: string;
}

// Home 元件是應用程式的主頁面。
export default function Home() {
  const [transcription, setTranscription] = useState('');
  const [isThinking, setIsThinking] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [textInput, setTextInput] = useState(''); // 新增文字輸入狀態
  const [conversation, setConversation] = useState<Message[]>([]);
  const [speechRate, setSpeechRate] = useState(2.0); // 預設語速改為最快
  const [selectedAI, setSelectedAI] = useState('Gemini'); // 新增 AI 選擇狀態
  
  const finalTranscriptionRef = useRef('');
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const speechPauseTimer = useRef<NodeJS.Timeout | null>(null);

  // 清除記憶的函式
  const handleClearMemory = async () => {
    try {
      const res = await fetch('/api/v1/memory/clear', {
        method: 'POST',
      });
      if (!res.ok) {
        throw new Error('無法清除伺服器記憶');
      }
      setConversation([]); // 清除前端對話
      console.log('記憶已成功清除。');
    } catch (error) {
      console.error("清除記憶失敗:", error);
    }
  };

  // 初始化並啟動語音辨識
  useEffect(() => {
    // 檢查瀏覽器是否支援 Web Speech API
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
      console.error("您的瀏覽器不支援 Web Speech API。");
      return;
    }

    const recognition = new SpeechRecognition();
    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'zh-TW';
    recognitionRef.current = recognition;

    // 處理語音辨識結果
    recognition.onresult = (event) => {
      let interimTranscript = '';
      let finalTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; ++i) {
        if (event.results[i].isFinal) {
          finalTranscript += event.results[i][0].transcript;
        } else {
          interimTranscript += event.results[i][0].transcript;
        }
      }
      
      setTranscription(interimTranscript);
      finalTranscriptionRef.current = finalTranscript || interimTranscript;

      // 重設斷點計時器
      if (speechPauseTimer.current) {
        clearTimeout(speechPauseTimer.current);
      }
      speechPauseTimer.current = setTimeout(() => {
        if (finalTranscriptionRef.current) {
          sendTranscriptionToAI();
        }
      }, 1200); // 1.2秒無新語音輸入則送出
    };

    recognition.onstart = () => setIsListening(true);
    recognition.onend = () => {
      setIsListening(false);
      // 自動重啟邏輯將由 toggleListening 控制
    };
    recognition.onerror = (event) => {
      console.error("語音辨識錯誤:", event.error);
      setIsListening(false);
    };

    recognition.start();

    const handleBeforeUnload = () => {
      handleClearMemory();
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // 元件卸載時清理
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      if (speechPauseTimer.current) clearTimeout(speechPauseTimer.current);
      if (recognitionRef.current) {
        recognitionRef.current.onend = null; // 避免重啟
        recognitionRef.current.stop();
        recognitionRef.current = null;
      }
    };
  }, []);

  // 播放 AI 回應
  useEffect(() => {
    if (conversation.length > 0) {
      const lastMessage = conversation[conversation.length - 1];
      if (lastMessage.role === 'model' && lastMessage.content) {
        speak(lastMessage.content);
      }
    }
  }, [conversation]);

  // 文字轉語音函式
  const speak = (text: string) => {
    if (typeof window !== 'undefined' && window.speechSynthesis) {
      cancelSpeaking(); // 先停止之前的播放
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = 'zh-TW';
      utterance.rate = speechRate; // 使用狀態中的語速
      window.speechSynthesis.speak(utterance);
    }
  };

  // 停止播放
  const cancelSpeaking = () => {
    if (typeof window !== 'undefined' && window.speechSynthesis) {
      window.speechSynthesis.cancel();
    }
  };

  // 控制麥克風的開關
  const toggleListening = () => {
    if (!recognitionRef.current) return;
    if (isListening) {
      recognitionRef.current.stop();
    } else {
      recognitionRef.current.start();
    }
  };

  // 將文字傳送給 AI (通用函式)
  const sendMessageToAI = async (messageContent: string) => {
    if (!messageContent) return;

    cancelSpeaking(); // 在思考前停止 AI 說話
    setIsThinking(true);
    const userMessage: Message = { role: 'user', content: messageContent };
    
    // 使用函數式更新以確保我們得到最新的狀態
    setConversation(prev => [...prev, userMessage]);
    
    // 清空來源
    setTranscription('');
    setTextInput('');
    finalTranscriptionRef.current = '';
    if (speechPauseTimer.current) clearTimeout(speechPauseTimer.current);

    try {
      const res = await fetch('/api/v1/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          message: messageContent,
          model: selectedAI,
        }),
      });

      const data = await res.json();
      if (!res.ok) {
        throw new Error(data.error || '後端請求失敗');
      }
      
      const aiMessage: Message = { role: 'model', content: data.response };
      setConversation(prev => [...prev, aiMessage]);

    } catch (error: any) {
      console.error(error);
      const errorMessage: Message = { role: 'model', content: error.message || '抱歉，我遇到了一些問題。' };
      setConversation(prev => [...prev, errorMessage]);
    } finally {
      setIsThinking(false);
    }
  };

  // 將語音轉錄的文字傳送給 AI
  const sendTranscriptionToAI = () => {
    if (finalTranscriptionRef.current) {
      sendMessageToAI(finalTranscriptionRef.current);
    }
  };

  // 處理文字輸入的送出
  const handleSendText = () => {
    sendMessageToAI(textInput);
  };

  return (
    <main className="flex min-h-screen flex-col items-center justify-center bg-gray-900 text-white p-4">
      <h1 className="text-4xl font-bold mb-4 text-center">AI 語音助理</h1>
      <div className="mb-4 text-sm text-gray-400">
        {isListening ? '正在聆聽...' : '麥克風已關閉'}
      </div>
      <ConversationView 
        conversation={conversation}
        isThinking={isThinking}
        transcription={transcription}
      />
      
      {/* 文字輸入框 */}
      <div className="w-full max-w-2xl mt-4 flex items-center space-x-2">
        <input
          type="text"
          value={textInput}
          onChange={(e) => setTextInput(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSendText()}
          className="flex-grow p-2 rounded-md bg-gray-800 border border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="請在這裡輸入訊息..."
        />
        <button
          onClick={handleSendText}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg font-semibold"
        >
          送出
        </button>
      </div>

      <div className="flex items-center justify-center space-x-4 mt-6 w-full max-w-2xl">
        <div className="flex flex-col items-center">
          <label htmlFor="speechRate" className="text-sm mb-1">語速</label>
          <input 
            id="speechRate"
            type="range" 
            min="0.5" 
            max="2" 
            step="0.1" 
            value={speechRate} 
            onChange={(e) => setSpeechRate(parseFloat(e.target.value))}
            className="w-32"
          />
        </div>
        <button 
          onClick={toggleListening}
          className={`px-4 py-2 rounded-lg font-semibold ${isListening ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'}`}
        >
          {isListening ? '停止麥克風' : '啟動麥克風'}
        </button>
        <button 
          onClick={cancelSpeaking}
          className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg font-semibold"
        >
          停止播放
        </button>
        <button 
          onClick={handleClearMemory}
          className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 rounded-lg font-semibold"
        >
          清除記憶
        </button>
        <div className="flex flex-col items-center">
          <label htmlFor="aiModel" className="text-sm mb-1">AI 模型</label>
          <select
            id="aiModel"
            value={selectedAI}
            onChange={(e) => setSelectedAI(e.target.value)}
            className="px-2 py-1 rounded-md bg-gray-800 border border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="Gemini">Gemini</option>
            <option value="Ollama">Ollama</option>
          </select>
        </div>
      </div>
    </main>
  );
}
