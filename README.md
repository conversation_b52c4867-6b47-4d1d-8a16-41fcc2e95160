# AI Customer Service System

A comprehensive voice-to-voice AI customer service system built with Next.js frontend and Go backend, featuring real-time speech recognition, AI-powered responses, and RAG (Retrieval-Augmented Generation) capabilities.

## 🌟 Features

- **Voice-to-Voice Interaction**: Real-time speech recognition and text-to-speech
- **Multi-AI Support**: Integration with Google Gemini and Ollama models
- **RAG System**: Context-aware responses using document knowledge base
- **Conversation History**: Persistent chat history with PostgreSQL
- **MCP Server**: Extensible tool system for external integrations
- **Responsive UI**: Modern React/Next.js interface with Tailwind CSS

## 🏗️ Architecture

```
├── frontend/          # Next.js React application
├── backend/           # Go REST API server
├── backend/mcp_server/# Model Context Protocol server
├── tools/indexing/    # RAG document indexing CLI
├── RAGData/          # Document storage for RAG
├── scripts/          # Development and deployment scripts
└── .taskmaster/      # Task management configuration
```

## 🚀 Quick Start

### Prerequisites

- **Go 1.21+** - Backend services
- **Node.js 18+** - Frontend application
- **PostgreSQL 14+** - Database with pgvector extension
- **Ollama** (optional) - Local AI model server
- **Google Gemini API Key** (optional) - Cloud AI service

### 1. Clone and Setup

```bash
git clone <repository-url>
cd AI_CustomerService2

# Copy environment configuration
cp .env.example .env
# Edit .env with your actual values
```

### 2. Database Setup

```bash
# Install PostgreSQL and pgvector extension
# Create database: taskmaster_db
# Update DATABASE_URL in .env
```

### 3. Start Development Environment

```bash
# Make scripts executable
chmod +x scripts/*.sh

# Start all services
./scripts/start-dev.sh
```

This will start:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8080
- MCP Server: http://localhost:8081

### 4. Index Documents (Optional)

```bash
# Add documents to RAGData/ directory
# Then run indexing
./scripts/index-documents.sh
```

## 📁 Project Structure

### Frontend (`frontend/`)
- **Next.js 15** with React 19
- **Tailwind CSS** for styling
- **Web Speech API** for voice interaction
- **TypeScript** for type safety

### Backend (`backend/`)
- **Go** REST API server
- **PostgreSQL** with pgvector for vector storage
- **Google Gemini** and **Ollama** AI integration
- **CORS** enabled for frontend communication

### MCP Server (`backend/mcp_server/`)
- Independent microservice for external tools
- Built-in tools: getCurrentTime, webSearch
- Extensible architecture for custom tools

### RAG Indexing (`tools/indexing/`)
- CLI tool for document processing
- Supports: TXT, PDF, DOCX, XLSX
- Generates embeddings and stores in vector database

## 🔧 Configuration

### Environment Variables

```bash
# Database
DATABASE_URL="postgresql://user:pass@localhost:5432/taskmaster_db?sslmode=disable"

# AI Services
GOOGLE_API_KEY="your_google_api_key"
OLLAMA_API_URL="http://localhost:11434"
OLLAMA_MODEL="llama3.2:3b"
OLLAMA_EMBEDDING_MODEL_NAME="nomic-embed-text"
GEMINI_MODEL="gemini-2.5-pro"

# System
SYSTEM_PROMPT="你是美而美早餐店的AI客服人員，請用親切友善的語氣回答客戶問題。"
PORT="8080"
```

## 🛠️ Development

### Backend Development

```bash
cd backend
go mod tidy
go run main.go
```

### Frontend Development

```bash
cd frontend
npm install
npm run dev
```

### RAG Document Indexing

```bash
cd tools/indexing
go run main.go --data ../../RAGData
```

### MCP Server Development

```bash
cd backend/mcp_server
go run main.go
```

## 📊 API Documentation

### Chat API

#### POST /api/v1/chat
Send a message to the AI assistant.

**Request:**
```json
{
  "message": "你好，請問有什麼早餐推薦？",
  "model": "Gemini"
}
```

**Response:**
```json
{
  "response": "您好！我推薦我們的招牌蛋餅和奶茶套餐..."
}
```

#### GET /api/v1/history
Get conversation history.

**Response:**
```json
[
  {
    "id": 1,
    "user_message": "你好",
    "ai_response": "您好！有什麼可以幫助您的嗎？",
    "timestamp": "2024-01-01T10:00:00Z",
    "session_id": "default"
  }
]
```

#### POST /api/v1/chat/clear
Clear conversation memory.

### MCP API

#### GET /tools
Get available tools.

#### POST /execute
Execute a tool.

**Request:**
```json
{
  "name": "getCurrentTime",
  "arguments": {
    "timezone": "UTC"
  }
}
```

## 🧪 Testing

### Backend Tests
```bash
cd backend
go test ./...
```

### Frontend Tests
```bash
cd frontend
npm test
```

### Integration Tests
```bash
# Start all services first
./scripts/start-dev.sh

# Run integration tests
curl http://localhost:8080/api/v1/health
curl http://localhost:8081/health
```

## 📦 Deployment

### Docker Deployment (Coming Soon)
```bash
docker-compose up -d
```

### Manual Deployment
1. Build frontend: `cd frontend && npm run build`
2. Build backend: `cd backend && go build -o server main.go`
3. Deploy to your server with PostgreSQL and environment variables

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📋 Task Management

This project uses Taskmaster for development task management. See `.taskmaster/` directory for task definitions and progress tracking.

### Key Tasks Completed:
- ✅ Project scaffolding and environment setup
- ✅ Frontend UI with Next.js and Tailwind CSS
- ✅ Backend server with PostgreSQL integration
- ✅ AI service integration (Gemini/Ollama)
- ✅ RAG document indexing system
- ✅ MCP server for external tools

### Remaining Tasks:
- 🔄 Enhanced error handling and logging
- 🔄 Performance optimization
- 🔄 Docker containerization
- 🔄 Production deployment configuration

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Google Gemini API for AI capabilities
- Ollama for local AI model support
- PostgreSQL and pgvector for vector storage
- Next.js and React teams for the frontend framework
- Go community for excellent libraries and tools