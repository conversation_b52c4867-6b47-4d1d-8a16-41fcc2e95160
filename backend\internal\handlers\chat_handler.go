package handlers

import (
	"encoding/json"
	"log"
	"net/http"

	"ai-customer-service/backend/internal/models"
	"ai-customer-service/backend/internal/services"
)

type Chat<PERSON>and<PERSON> struct {
	chatService *services.ChatService
}

func NewChatHandler(chatService *services.ChatService) *ChatHandler {
	return &ChatHandler{
		chatService: chatService,
	}
}

func (h *ChatHandler) HandleChat(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	var req models.ChatRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	if req.Message == "" {
		http.Error(w, "Message is required", http.StatusBadRequest)
		return
	}

	// Default to Gemini if no model specified
	if req.Model == "" {
		req.Model = "Gemini"
	}

	response, err := h.chatService.ProcessMessage(r.Context(), req.Message, req.Model)
	if err != nil {
		log.Printf("Error processing message: %v", err)
		resp := models.ChatResponse{
			Error: "抱歉，我遇到了一些問題，請稍後再試。",
		}
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(resp)
		return
	}

	resp := models.ChatResponse{
		Response: response,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(resp)
}

func (h *ChatHandler) HandleClearChat(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if err := h.chatService.ClearMemory(); err != nil {
		log.Printf("Error clearing memory: %v", err)
		http.Error(w, "Failed to clear memory", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"status": "success"})
}

func (h *ChatHandler) HandleGetHistory(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	history, err := h.chatService.GetHistory(r.Context())
	if err != nil {
		log.Printf("Error getting history: %v", err)
		http.Error(w, "Failed to get history", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(history)
}
