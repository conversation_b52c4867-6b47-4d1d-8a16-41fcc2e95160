# Task ID: 3
# Title: Frontend UI Scaffolding with Next.js and Tailwind CSS
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Create the Next.js frontend application and build the static UI layout based on the PRD's requirements, including the header, conversation area, and voice control button.
# Details:
Inside the `frontend/` directory, initialize a new Next.js application using `npx create-next-app@latest`. Integrate Tailwind CSS for styling. Create React components for the main layout: `Header.js`, `ConversationArea.js`, and `Controls.js`. The `Controls.js` component should feature a prominent, center-bottom button for 'Start/Stop Voice'. Use React Context for initial state management (e.g., `isListening`, `conversation`). The layout should be responsive.

# Test Strategy:
Run the Next.js development server. Verify the UI matches the layout described in the PRD and the `UI.png` mockup. Test responsiveness by resizing the browser window to check mobile and desktop views. Ensure all static UI elements are present and styled correctly.
