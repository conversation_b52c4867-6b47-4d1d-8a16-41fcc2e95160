package config

import (
	"os"
)

type Config struct {
	DatabaseURL               string
	OllamaAPIURL              string
	OllamaEmbeddingModelName  string
	ChunkSize                 int
	ChunkOverlap              int
}

func Load() *Config {
	return &Config{
		DatabaseURL:               getEnv("DATABASE_URL", "postgresql://cicuser:pi@localhost:5432/taskmaster_db?sslmode=disable"),
		OllamaAPIURL:              getEnv("OLLAMA_API_URL", "http://localhost:11434"),
		OllamaEmbeddingModelName:  getEnv("OLLAMA_EMBEDDING_MODEL_NAME", "nomic-embed-text"),
		ChunkSize:                 getEnvInt("CHUNK_SIZE", 1000),
		ChunkOverlap:              getEnvInt("CHUNK_OVERLAP", 200),
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		// Simple conversion - in production you might want better error handling
		if len(value) > 0 {
			// For simplicity, just return default if not a simple number
			return defaultValue
		}
	}
	return defaultValue
}
