@echo off
echo 🚀 Starting AI Customer Service Development Environment (Windows)

REM Check if .env file exists
if not exist ".env" (
    echo ⚠️  .env file not found. Copying from .env.example...
    copy .env.example .env
    echo 📝 Please edit .env file with your actual configuration values
    pause
)

REM Check prerequisites
echo 🔍 Checking prerequisites...

where go >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Go is not installed. Please install Go 1.21 or later.
    pause
    exit /b 1
)

where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js.
    pause
    exit /b 1
)

where psql >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  PostgreSQL client not found. Make sure PostgreSQL is installed and running.
)

echo ✅ Prerequisites check completed

REM Start backend server
echo 🔧 Starting backend server...
cd backend
start "Backend Server" cmd /k "go run main.go"
cd ..

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start MCP server
echo 🔧 Starting MCP server...
cd backend\mcp_server
start "MCP Server" cmd /k "go run main.go"
cd ..\..

REM Wait a moment for MCP server to start
timeout /t 3 /nobreak >nul

REM Start frontend
echo 🔧 Starting frontend...
cd frontend
if not exist "node_modules" (
    echo 📦 Installing frontend dependencies...
    npm install
)
start "Frontend Server" cmd /k "npm run dev"
cd ..

echo 🎉 All services started!
echo 📱 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:8080
echo 🛠️  MCP Server: http://localhost:8081
echo.
echo Press any key to open the application in your browser...
pause >nul
start http://localhost:3000

echo.
echo 💡 To stop services, close the respective command windows
echo 📊 To monitor services, use: scripts\monitor-system-windows.bat
pause
