#!/bin/bash

# System Monitoring Script for AI Customer Service System

echo "📊 AI Customer Service System Monitor"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check service health
check_service() {
    local service_name=$1
    local url=$2
    local expected_status=${3:-200}
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "$expected_status"; then
        echo -e "${GREEN}✅ $service_name is healthy${NC}"
        return 0
    else
        echo -e "${RED}❌ $service_name is not responding${NC}"
        return 1
    fi
}

# Function to check Docker service
check_docker_service() {
    local service_name=$1
    
    if docker-compose ps "$service_name" | grep -q "Up"; then
        echo -e "${GREEN}✅ $service_name container is running${NC}"
        return 0
    else
        echo -e "${RED}❌ $service_name container is not running${NC}"
        return 1
    fi
}

# Check if running in Docker environment
if command -v docker-compose &> /dev/null && [ -f "docker-compose.yml" ]; then
    echo -e "${BLUE}🐳 Docker Environment Detected${NC}"
    echo ""
    
    # Check Docker services
    echo "📦 Container Status:"
    check_docker_service "postgres"
    check_docker_service "backend"
    check_docker_service "mcp_server"
    check_docker_service "frontend"
    
    # Check if Ollama is running
    if docker-compose ps ollama | grep -q "Up"; then
        check_docker_service "ollama"
    else
        echo -e "${YELLOW}⚠️  Ollama container is not running (optional)${NC}"
    fi
    
    echo ""
    echo "🔗 Service Health Checks:"
else
    echo -e "${BLUE}🔧 Local Development Environment${NC}"
    echo ""
    echo "🔗 Service Health Checks:"
fi

# Health checks
check_service "Frontend" "http://localhost:3000"
check_service "Backend API" "http://localhost:8080/api/v1/health"
check_service "MCP Server" "http://localhost:8081/health"

# Database check
echo ""
echo "🗄️  Database Status:"
if command -v docker-compose &> /dev/null && docker-compose ps postgres | grep -q "Up"; then
    if docker-compose exec postgres pg_isready -U cicuser -d taskmaster_db > /dev/null 2>&1; then
        echo -e "${GREEN}✅ PostgreSQL is ready${NC}"
        
        # Get database stats
        CONN_COUNT=$(docker-compose exec -T postgres psql -U cicuser -d taskmaster_db -t -c "SELECT count(*) FROM pg_stat_activity WHERE datname='taskmaster_db';" | tr -d ' ')
        CONV_COUNT=$(docker-compose exec -T postgres psql -U cicuser -d taskmaster_db -t -c "SELECT count(*) FROM conversation_history;" | tr -d ' ')
        RAG_COUNT=$(docker-compose exec -T postgres psql -U cicuser -d taskmaster_db -t -c "SELECT count(*) FROM rag_documents;" | tr -d ' ')
        
        echo "  📈 Active connections: $CONN_COUNT"
        echo "  💬 Conversation records: $CONV_COUNT"
        echo "  📚 RAG documents: $RAG_COUNT"
    else
        echo -e "${RED}❌ PostgreSQL is not ready${NC}"
    fi
elif command -v psql &> /dev/null; then
    if [ -f ".env" ]; then
        export $(grep -v '^#' .env | xargs)
        if psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ PostgreSQL is ready${NC}"
        else
            echo -e "${RED}❌ PostgreSQL connection failed${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  .env file not found${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Cannot check database status${NC}"
fi

# System resources (if running in Docker)
if command -v docker &> /dev/null; then
    echo ""
    echo "💻 System Resources:"
    
    # Docker stats
    echo "📊 Container Resource Usage:"
    docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}" $(docker-compose ps -q) 2>/dev/null || echo "No containers running"
fi

# Recent logs check
echo ""
echo "📝 Recent Activity:"
if command -v docker-compose &> /dev/null && [ -f "docker-compose.yml" ]; then
    echo "🔍 Last 5 backend log entries:"
    docker-compose logs --tail=5 backend 2>/dev/null || echo "No backend logs available"
else
    echo "Local development - check individual service logs"
fi

# API endpoint tests
echo ""
echo "🧪 API Tests:"

# Test chat endpoint
CHAT_TEST=$(curl -s -X POST http://localhost:8080/api/v1/chat \
    -H "Content-Type: application/json" \
    -d '{"message":"健康檢查","model":"Gemini"}' \
    -w "%{http_code}" -o /dev/null)

if [ "$CHAT_TEST" = "200" ]; then
    echo -e "${GREEN}✅ Chat API is working${NC}"
else
    echo -e "${RED}❌ Chat API test failed (HTTP $CHAT_TEST)${NC}"
fi

# Test MCP tools endpoint
MCP_TEST=$(curl -s http://localhost:8081/tools -w "%{http_code}" -o /dev/null)

if [ "$MCP_TEST" = "200" ]; then
    echo -e "${GREEN}✅ MCP Tools API is working${NC}"
else
    echo -e "${RED}❌ MCP Tools API test failed (HTTP $MCP_TEST)${NC}"
fi

echo ""
echo "📋 Summary:"
echo "==========="
echo "🌐 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8080"
echo "🛠️  MCP Server: http://localhost:8081"
echo "🗄️  Database: localhost:5432"
echo ""
echo "💡 Commands:"
echo "   View logs: docker-compose logs -f [service]"
echo "   Restart: docker-compose restart [service]"
echo "   Stop all: docker-compose down"
echo ""
echo "🔄 Monitor refreshed at: $(date)"
