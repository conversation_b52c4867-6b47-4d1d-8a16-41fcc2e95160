# Task ID: 6
# Title: Integrate Frontend/Backend and Implement Text-to-Speech (TTS)
# Status: pending
# Dependencies: 4, 5
# Priority: high
# Description: Connect the frontend to the backend chat API and implement Text-to-Speech (TTS) to play back the AI's response, completing the core voice-to-voice interaction loop.
# Details:
Modify the frontend STT logic (from Task 4) to send the final transcribed text to the backend `/api/chat` endpoint (from Task 5). Upon receiving the AI's text response, use the `SpeechSynthesis` interface of the Web Speech API to convert the text to audio and play it. Use `speechSynthesis.speak()` with a `SpeechSynthesisUtterance` object. Implement logic to interrupt the current speech playback if the user starts speaking again.

# Test Strategy:
Perform an end-to-end test: 1. Speak a query. 2. Verify it's transcribed and sent to the backend. 3. Verify the backend returns an AI response. 4. Verify the frontend speaks the response aloud. Test the interrupt functionality by speaking while the AI is responding.
