{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/nodejs_work/AI_CustomerService2/frontend/src/app/api/v1/chat/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n// This is a proxy route to handle CORS and provide a consistent API interface\n// The actual backend server should be running on localhost:8080\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    \n    // Forward the request to the Go backend\n    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8080';\n    const response = await fetch(`${backendUrl}/api/v1/chat`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(body),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ error: 'Backend request failed' }));\n      return NextResponse.json(errorData, { status: response.status });\n    }\n\n    const data = await response.json();\n    return NextResponse.json(data);\n  } catch (error) {\n    console.error('API route error:', error);\n    return NextResponse.json(\n      { error: '無法連接到後端服務，請確認後端服務器正在運行' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'POST, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAKO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,wCAAwC;QACxC,MAAM,aAAa,6DAA2B;QAC9C,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,YAAY,CAAC,EAAE;YACxD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,OAAO;gBAAyB,CAAC;YACxF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,WAAW;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAChE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}