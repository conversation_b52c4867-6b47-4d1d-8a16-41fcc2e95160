package handlers

import (
	"encoding/json"
	"log"
	"net/http"

	"ai-customer-service/backend/mcp_server/internal/models"
	"ai-customer-service/backend/mcp_server/internal/tools"
)

type ToolHandler struct {
	toolManager *tools.ToolManager
}

func NewToolHandler(toolManager *tools.ToolManager) *ToolHandler {
	return &ToolHandler{
		toolManager: toolManager,
	}
}

func (h *ToolHandler) HandleGetTools(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	tools := h.toolManager.GetAllTools()
	response := models.ToolsResponse{
		Tools: tools,
	}

	w.<PERSON>er().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (h *ToolHandler) HandleExecuteTool(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		w.Write<PERSON>ead<PERSON>(http.StatusOK)
		return
	}

	var req models.ExecuteRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	if req.Name == "" {
		http.Error(w, "Tool name is required", http.StatusBadRequest)
		return
	}

	result, err := h.toolManager.ExecuteTool(r.Context(), req.Name, req.Arguments)
	if err != nil {
		log.Printf("Error executing tool %s: %v", req.Name, err)
		resp := models.ExecuteResponse{
			Error: err.Error(),
		}
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(resp)
		return
	}

	resp := models.ExecuteResponse{
		Result: result,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(resp)
}
