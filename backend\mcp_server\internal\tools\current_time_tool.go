package tools

import (
	"context"
	"time"

	"ai-customer-service/backend/mcp_server/internal/models"
)

type CurrentTimeTool struct{}

func (t *CurrentTimeTool) GetSchema() models.ToolSchema {
	return models.ToolSchema{
		Name:        "getCurrentTime",
		Description: "Get the current date and time",
		Parameters: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"timezone": map[string]interface{}{
					"type":        "string",
					"description": "Timezone (optional, defaults to local)",
				},
			},
		},
	}
}

func (t *CurrentTimeTool) Execute(ctx context.Context, args map[string]interface{}) (string, error) {
	timezone := "Local"
	if tz, ok := args["timezone"].(string); ok && tz != "" {
		timezone = tz
	}

	var currentTime time.Time
	if timezone == "Local" {
		currentTime = time.Now()
	} else {
		loc, err := time.LoadLocation(timezone)
		if err != nil {
			return "", err
		}
		currentTime = time.Now().In(loc)
	}

	return currentTime.Format("2006-01-02 15:04:05 MST"), nil
}
